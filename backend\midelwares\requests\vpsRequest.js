/**
 * VPS Request Validation Middleware
 * Validates VPS-related requests using Joi
 */

const Joi = require('joi');
const { getDefaultOptions } = require('./sharedDefaultOptions');

/**
 * Validation for creating VPS order
 */
const createVPSOrderValidator = async (req, res, next) => {
  const schema = Joi.object({
    packageId: Joi.string()
      .required()
      .label('Package ID'),

    region: Joi.string()
      .required()
      .label('Region'),

    operatingSystem: Joi.string()
      .valid('ubuntu-22.04', 'ubuntu-20.04', 'debian-11', 'centos-8', 'windows-2019')
      .required()
      .label('Operating System'),

    displayName: Joi.string()
      .min(3)
      .max(50)
      .pattern(/^[a-zA-Z0-9-_]+$/)
      .required()
      .label('Display Name'),

    billingCycle: Joi.string()
      .valid('hourly', 'monthly', 'yearly')
      .default('monthly')
      .label('Billing Cycle'),

    billingInfo: Joi.object({
      name: Joi.string()
        .min(2)
        .max(100)
        .required()
        .label('Billing Name'),

      email: Joi.string()
        .email()
        .required()
        .label('Billing Email'),

      phone: Joi.string()
        .pattern(/^\+?\d{10,}$/)
        .optional()
        .label('Phone Number'),

      address: Joi.string()
        .max(255)
        .optional()
        .label('Address'),

      country: Joi.string()
        .length(2)
        .optional()
        .label('Country'),

      isCompany: Joi.boolean()
        .default(false)
        .label('Is Company'),

      companyName: Joi.when('isCompany', {
        is: true,
        then: Joi.string().min(2).max(100).required(),
        otherwise: Joi.optional()
      }).label('Company Name'),

      companyICE: Joi.when('isCompany', {
        is: true,
        then: Joi.string().pattern(/^\d{15}$/),
        otherwise: Joi.optional()
      }).label('Company ICE'),

      companyAddress: Joi.when('isCompany', {
        is: true,
        then: Joi.string().max(255),
        otherwise: Joi.optional()
      }).label('Company Address'),

      companyPhone: Joi.when('isCompany', {
        is: true,
        then: Joi.string().pattern(/^\+?\d{10,}$/),
        otherwise: Joi.optional()
      }).label('Company Phone'),

      companyEmail: Joi.when('isCompany', {
        is: true,
        then: Joi.string().email(),
        otherwise: Joi.optional()
      }).label('Company Email')
    }).required().label('Billing Information'),

    sshKeys: Joi.array()
      .items(Joi.string().min(100).max(2000))
      .optional()
      .label('SSH Keys')
  });

  const result = schema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req)
  });

  if (result.error) {
    const errorArray = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed.",
      errors: errorArray,
    });
  }

  next();
};

/**
 * Validation for VPS instance control actions
 */
const controlInstanceValidator = async (req, res, next) => {
  const schema = Joi.object({
    action: Joi.string()
      .valid('start', 'stop', 'restart', 'reset-password')
      .required()
      .label('Action')
  });

  const paramSchema = Joi.object({
    instanceId: Joi.string()
      .required()
      .label('Instance ID')
  });

  const bodyResult = schema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req)
  });

  const paramResult = paramSchema.validate(req.params, {
    abortEarly: false,
    ...getDefaultOptions(req)
  });

  const errors = [];
  if (bodyResult.error) {
    errors.push(...bodyResult.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    })));
  }

  if (paramResult.error) {
    errors.push(...paramResult.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    })));
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: "Validation failed.",
      errors: errors,
    });
  }

  next();
};

/**
 * Simple validation middleware for query parameters
 */
const validateQueryProvider = async (req, res, next) => {
  if (req.query.provider && !['contabo', 'digitalocean', 'vultr', 'linode'].includes(req.query.provider)) {
    return res.status(400).json({
      success: false,
      message: "Invalid provider. Supported providers: contabo, digitalocean, vultr, linode"
    });
  }
  next();
};

/**
 * Simple validation for instance ID parameter
 */
const validateInstanceId = async (req, res, next) => {
  if (!req.params.instanceId) {
    return res.status(400).json({
      success: false,
      message: "Instance ID is required"
    });
  }
  next();
};

/**
 * Simple validation for plan ID parameter
 */
const validatePlanId = async (req, res, next) => {
  if (!req.params.planId) {
    return res.status(400).json({
      success: false,
      message: "Plan ID is required"
    });
  }
  next();
};

/**
 * Validation for payment confirmation
 */
const confirmPaymentValidator = async (req, res, next) => {
  const schema = Joi.object({
    orderId: Joi.string()
      .required()
      .label('Order ID'),

    paymentId: Joi.string()
      .required()
      .label('Payment ID'),

    transactionId: Joi.string()
      .required()
      .label('Transaction ID')
  });

  const result = schema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req)
  });

  if (result.error) {
    const errorArray = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed.",
      errors: errorArray,
    });
  }

  next();
};

/**
 * Validation for creating snapshot
 */
const createSnapshotValidator = async (req, res, next) => {
  const schema = Joi.object({
    snapshotName: Joi.string()
      .min(3)
      .max(50)
      .pattern(/^[a-zA-Z0-9-_]+$/)
      .optional()
      .label('Snapshot Name')
  });

  const result = schema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req)
  });

  if (result.error) {
    const errorArray = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed.",
      errors: errorArray,
    });
  }

  next();
};

/**
 * Validation for reinstalling instance
 */
const reinstallInstanceValidator = async (req, res, next) => {
  const schema = Joi.object({
    imageId: Joi.string()
      .required()
      .label('Image ID')
  });

  const result = schema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req)
  });

  if (result.error) {
    const errorArray = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed.",
      errors: errorArray,
    });
  }

  next();
};

/**
 * Custom validation for VPS configuration
 */
const validateVPSConfig = (req, res, next) => {
  const { planId, region, operatingSystem, provider } = req.body;
  
  // Custom validation logic can be added here
  // For example, checking if the combination of plan, region, and OS is valid
  
  // Region validation based on provider
  const providerRegions = {
    contabo: ['EU', 'US-EAST', 'US-WEST', 'ASIA'],
    digitalocean: ['nyc1', 'nyc3', 'ams3', 'sgp1', 'lon1', 'fra1'],
    vultr: ['ewr', 'ord', 'dfw', 'sea', 'lax', 'atl', 'ams', 'lhr', 'fra', 'sjc'],
    linode: ['us-east', 'us-west', 'eu-west', 'ap-south', 'ap-northeast']
  };

  const selectedProvider = provider || 'contabo';
  const validRegions = providerRegions[selectedProvider];

  if (validRegions && !validRegions.includes(region)) {
    return res.status(400).json({
      success: false,
      message: `Invalid region for ${selectedProvider}. Valid regions: ${validRegions.join(', ')}`
    });
  }

  next();
};

/**
 * Rate limiting validation for VPS operations
 */
const rateLimitVPSOperations = (req, res, next) => {
  // This can be enhanced with Redis-based rate limiting
  // For now, it's a placeholder for future implementation
  next();
};

module.exports = {
  createVPSOrderValidator,
  controlInstanceValidator,
  validateQueryProvider,
  validateInstanceId,
  validatePlanId,
  confirmPaymentValidator,
  createSnapshotValidator,
  reinstallInstanceValidator,
  validateVPSConfig,
  rateLimitVPSOperations
};
