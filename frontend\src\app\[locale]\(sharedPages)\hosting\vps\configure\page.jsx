"use client";
import { useState, useEffect } from "react";
import { useSear<PERSON><PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Typo<PERSON>, <PERSON><PERSON>, Card, CardBody } from "@material-tailwind/react";
import vpsService from "@/app/services/vpsService";
import cartService from "@/app/services/cartService";
import packageService from "@/app/services/packageService";
import { useAuth } from "@/app/context/AuthContext";
import { toast } from "react-toastify";
import {
  ServerIcon,
  CpuIcon,
  HardDriveIcon,
  GlobeIcon,
  ShieldIcon,
  ClockIcon,
  CheckIcon,
  ArrowLeftIcon,
  MonitorIcon,
  TerminalIcon,
  EyeIcon,
  EyeOffIcon,
  KeyIcon,
  UserIcon
} from "lucide-react";

// Modern OS Icons Components
const UbuntuIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z"/>
    </svg>
  </div>
);

const CentOSIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z"/>
    </svg>
  </div>
);

const DebianIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
    </svg>
  </div>
);

const WindowsIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z"/>
    </svg>
  </div>
);

export default function ConfigureVPSPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations("vps_configure");
  const { setCartCount } = useAuth();

  // State management
  const [vpsPlans, setVpsPlans] = useState([]);
  const [osImages, setOsImages] = useState([]);
  const [regions, setRegions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [orderLoading, setOrderLoading] = useState(false);

  // Initialize plan from URL params
  const planId = searchParams.get('plan');
  const autoBackup = searchParams.get('autobackup') === 'true';

  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isAutoBackup, setIsAutoBackup] = useState(autoBackup);

  // Function to parse specifications from database
  const parseSpecifications = (specifications, description) => {
    let cores = 0, ram = "0 GB", storage = "0 GB", traffic = "32 TB";

    // Parse from specifications array
    if (specifications && Array.isArray(specifications)) {
      specifications.forEach(spec => {
        const value = spec.value || "";
        const lowerValue = value.toLowerCase();

        // Parse CPU cores
        if (lowerValue.includes("cpu") || lowerValue.includes("core") || lowerValue.includes("vcpu")) {
          const cpuMatch = value.match(/(\d+)/);
          if (cpuMatch) cores = parseInt(cpuMatch[1]);
        }

        // Parse RAM
        if (lowerValue.includes("ram") || lowerValue.includes("memory") || lowerValue.includes("gb ram")) {
          const ramMatch = value.match(/(\d+)\s*gb/i);
          if (ramMatch) ram = `${ramMatch[1]} GB`;
        }

        // Parse Storage
        if (lowerValue.includes("storage") || lowerValue.includes("disk") || lowerValue.includes("ssd") || lowerValue.includes("nvme")) {
          const storageMatch = value.match(/(\d+)\s*gb/i);
          if (storageMatch) {
            const storageType = lowerValue.includes("nvme") ? "NVMe" : lowerValue.includes("ssd") ? "SSD" : "";
            storage = `${storageMatch[1]} GB ${storageType}`.trim();
          }
        }

        // Parse Traffic/Bandwidth
        if (lowerValue.includes("traffic") || lowerValue.includes("bandwidth") || lowerValue.includes("transfer")) {
          const trafficMatch = value.match(/(\d+)\s*(tb|gb)/i);
          if (trafficMatch) {
            traffic = `${trafficMatch[1]} ${trafficMatch[2].toUpperCase()}`;
          }
        }
      });
    }

    // Fallback: parse from description if specifications are empty
    if (cores === 0 && description) {
      const descLower = description.toLowerCase();
      const cpuMatch = description.match(/(\d+)\s*(cpu|core|vcpu)/i);
      if (cpuMatch) cores = parseInt(cpuMatch[1]);

      const ramMatch = description.match(/(\d+)\s*gb\s*ram/i);
      if (ramMatch) ram = `${ramMatch[1]} GB`;

      const storageMatch = description.match(/(\d+)\s*gb\s*(storage|disk|ssd|nvme)/i);
      if (storageMatch) {
        const storageType = descLower.includes("nvme") ? "NVMe" : descLower.includes("ssd") ? "SSD" : "";
        storage = `${storageMatch[1]} GB ${storageType}`.trim();
      }
    }

    return { cores, ram, storage, traffic };
  };

  // Fetch VPS packages and find the selected one
  useEffect(() => {
    const fetchVPSPackages = async () => {
      try {
        setLoading(true);

        // Récupérer les packages VPS depuis la base de données
        const response = await packageService.getPackages("VPS Hosting");
        console.log("VPS packages response:", response);

        let vpsPackages = [];
        if (response.data && Array.isArray(response.data)) {
          vpsPackages = response.data;
        } else if (response.data && response.data.packages && Array.isArray(response.data.packages)) {
          vpsPackages = response.data.packages;
        } else if (Array.isArray(response)) {
          vpsPackages = response;
        }

        // Transformer les packages de la base de données
        const transformedPlans = vpsPackages.map(pkg => {
          const specs = parseSpecifications(pkg.specifications, pkg.description);
          return {
            id: pkg._id,
            _id: pkg._id,
            name: pkg.name,
            price: pkg.price,
            cores: specs.cores,
            ram: specs.ram,
            storage: specs.storage,
            traffic: specs.traffic,
            description: pkg.description,
            specifications: pkg.specifications
          };
        });

        setVpsPlans(transformedPlans);

        // Trouver le package sélectionné par son ID
        if (planId && transformedPlans.length > 0) {
          const foundPlan = transformedPlans.find(plan =>
            plan._id === planId ||
            plan.id === planId ||
            plan._id?.toString() === planId ||
            plan.id?.toString() === planId
          );
          if (foundPlan) {
            setSelectedPlan(foundPlan);
            console.log("Selected plan found:", foundPlan);
          } else {
            console.error("Plan not found with ID:", planId);
            console.log("Available plans:", transformedPlans.map(p => ({ id: p.id, _id: p._id, name: p.name })));
            setError("Package VPS non trouvé");
          }
        }

      } catch (error) {
        console.error("Error fetching VPS plans:", error);
        setError("Erreur lors du chargement des plans VPS");
        setVpsPlans([]);
        setSelectedPlan(null);
      } finally {
        setLoading(false);
      }
    };

    // Fetch dynamic OS images from API
    const fetchOSImages = async () => {
      try {
        console.log("🔍 Fetching OS images from API...");
        const response = await vpsService.getImages('contabo');
        console.log("✅ OS Images response:", response);

        let images = [];
        if (response.data.data && Array.isArray(response.data.data)) {
          images = response.data.data;
        } else if (response.data && response.data.images && Array.isArray(response.data.images)) {
          images = response.data.images;
        }

        // Transform API data to expected format
        const transformedImages = images.map(img => ({
          id: img.imageId || img.id,
          name: img.name,
          description: img.description,
          type: img.osType || 'linux',
          version: img.version,
          provider: img.provider
        }));

        console.log("🔄 Setting OS images:", transformedImages.length, "images");
        setOsImages(transformedImages);

        // Set default OS if available
        if (transformedImages.length > 0) {
          const defaultOS = transformedImages.find(img =>
            img.name.toLowerCase().includes('ubuntu') &&
            img.name.toLowerCase().includes('22.04')
          ) || transformedImages[0];
          console.log("🔄 Setting default OS:", defaultOS.name, defaultOS.id);
          setSelectedOS(defaultOS.id);
        }
      } catch (error) {
        console.error("❌ Error fetching OS images:", error);
        console.error("Error details:", error.response?.data || error.message);
        // Fallback to static data if API fails
        const staticOsImages = [
          { id: "ubuntu-20.04", name: "Ubuntu 20.04 LTS", type: "ubuntu" },
          { id: "ubuntu-22.04", name: "Ubuntu 22.04 LTS", type: "ubuntu" },
          { id: "centos-7", name: "CentOS 7", type: "centos" },
          { id: "debian-11", name: "Debian 11", type: "debian" }
        ];
        setOsImages(staticOsImages);
        setSelectedOS("ubuntu-22.04");
      }
    };

    // Fetch dynamic regions from API
    const fetchRegions = async () => {
      try {
        console.log("🔍 Fetching regions from API...");
        const response = await vpsService.getRegions('contabo');
        console.log("✅ Regions response:", response);

        let regions = [];
        if (response.data.data && Array.isArray(response.data.data)) {
          regions = response.data.data;
        } else if (response.data && response.data.regions && Array.isArray(response.data.regions)) {
          regions = response.data.regions;
        }

        // Transform API data to expected format
        const transformedRegions = regions.map(region => ({
          id: region.regionSlug,
          name: region.regionName,
          provider: region.provider
        }));

        console.log("🔄 Setting regions:", transformedRegions.length, "regions");
        setRegions(transformedRegions);

        // Set default region if available
        if (transformedRegions.length > 0) {
          const defaultRegion = transformedRegions.find(region =>
            region.id === 'EU'
          ) || transformedRegions[0];
          console.log("🔄 Setting default region:", defaultRegion.name, defaultRegion.id);
          setSelectedLocation(defaultRegion.id);
        }
      } catch (error) {
        console.error("❌ Error fetching regions:", error);
        console.error("Error details:", error.response?.data || error.message);
        // Fallback to static data if API fails
        const staticRegions = [
          { id: "EU", name: "European Union", description: "Germany", country: "Germany", city: "Nuremberg" },
          { id: "US-central", name: "United States Central", description: "St. Louis", country: "United States", city: "St. Louis" },
          { id: "SG", name: "Asia Pacific", description: "Singapore", country: "Singapore", city: "Singapore" }
        ];
        setRegions(staticRegions);
        setSelectedLocation("EU");
      }
    };

    // Appeler les fonctions pour récupérer les données
    fetchVPSPackages();
    fetchOSImages();
    fetchRegions();


  }, [planId]);

  // Configuration state
  const [selectedOS, setSelectedOS] = useState("ubuntu-20.04");
  const [selectedLocation, setSelectedLocation] = useState("france");
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [additionalIPs, setAdditionalIPs] = useState(0);
  const [backupEnabled, setBackupEnabled] = useState(autoBackup);
  const [quantity, setQuantity] = useState(1);

  // New Contabo-style options
  const [autoBackupOption, setAutoBackupOption] = useState("none");
  const [privateNetworking, setPrivateNetworking] = useState("none");
  const [ipv4Addresses, setIpv4Addresses] = useState(1);
  const [objectStorage, setObjectStorage] = useState("none");
  const [serverManagement, setServerManagement] = useState("unmanaged");
  const [monitoring, setMonitoring] = useState("none");
  const [ssl, setSsl] = useState("none");

  // Login credentials state
  const [username, setUsername] = useState("root");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  // Password validation function
  const validatePassword = (pwd) => {
    if (!pwd) {
      return "Please enter a valid password";
    }
    if (pwd.length < 8) {
      return "Password must be at least 8 characters long";
    }
    if (!/(?=.*[a-z])/.test(pwd)) {
      return "Password must contain at least one lowercase letter";
    }
    if (!/(?=.*[A-Z])/.test(pwd)) {
      return "Password must contain at least one uppercase letter";
    }
    if (!/(?=.*\d)/.test(pwd)) {
      return "Password must contain at least one number";
    }
    if (!/(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(pwd)) {
      return "Password must contain at least one special character";
    }
    return "";
  };

  // Generate secure password function
  const generateSecurePassword = () => {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const allChars = lowercase + uppercase + numbers + symbols;

    let newPassword = '';

    // Ensure at least one character from each category
    newPassword += lowercase[Math.floor(Math.random() * lowercase.length)];
    newPassword += uppercase[Math.floor(Math.random() * uppercase.length)];
    newPassword += numbers[Math.floor(Math.random() * numbers.length)];
    newPassword += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < 16; i++) {
      newPassword += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password
    newPassword = newPassword.split('').sort(() => Math.random() - 0.5).join('');

    setPassword(newPassword);
    setPasswordError("");
  };

  // Handle password change
  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordError(validatePassword(newPassword));
  };

  // Generate initial password on component mount
  useEffect(() => {
    if (!password) {
      generateSecurePassword();
    }
  }, []);

  // Handle adding VPS to cart
  const handleAddToCart = async () => {
    if (!selectedPlan) {
      toast.error("Veuillez sélectionner un plan VPS");
      return;
    }

    // Validate password
    const passwordValidationError = validatePassword(password);
    if (passwordValidationError) {
      setPasswordError(passwordValidationError);
      toast.error("Please enter a valid password");
      return;
    }

    try {
      setOrderLoading(true);

      // Vérifier si nous avons un ID valide
      const packageId = selectedPlan._id || selectedPlan.id;

      if (!packageId) {
        toast.error("ID du package VPS manquant. Veuillez réessayer.");
        return;
      }

      // Map frontend selections to Contabo API format
      const contaboRegionMap = {
        'france': 'EU',
        'EU': 'EU',
        'germany': 'EU',
        'US-central': 'US-east',
        'usa': 'US-east',
        'SG': 'SIN',
        'singapore': 'SIN',
        'asia': 'SIN'
      };

      const contaboOSMap = {
        'ubuntu-20.04': 'ubuntu-20.04',
        'ubuntu-22.04': 'ubuntu-22.04',
        'ubuntu-24.04': 'ubuntu-24.04',
        'centos-7': 'centos-7',
        'centos-8': 'centos-8',
        'debian-10': 'debian-10',
        'debian-11': 'debian-11',
        'windows-2019': 'windows-server-2019',
        'windows-2022': 'windows-server-2022'
      };

      // Generate display name if not provided
      const displayName = `${selectedPlan.name}-${Date.now()}`;

      // Préparer les données pour l'ajout au panier avec configuration Contabo
      const cartData = {
        packageId: packageId,
        quantity: quantity,
        period: selectedPeriod === 'monthly' ? 1 : selectedPeriod === '6months' ? 6 : 12,
        // Configuration personnalisée pour Contabo VPS
        customConfiguration: {
          // Contabo API fields
          planId: selectedPlan.vpsConfig?.providerProductId || selectedPlan.id, // V91, V92, etc.
          provider: 'contabo',
          region: contaboRegionMap[selectedLocation] || 'EU',
          operatingSystem: contaboOSMap[selectedOS] || selectedOS,
          displayName: displayName,
          username: username,
          rootPassword: password,
          sshKeys: [], // Will be added later if user provides
          userData: '', // Cloud-init script if needed
          addons: {
            privatenetworking: privateNetworking !== 'none',
            autobackup: autoBackupOption !== 'none',
            monitoring: monitoring !== 'none'
          },
          // Plan specifications for reference
          cpu: selectedPlan.cores || selectedPlan.cpu,
          ram: selectedPlan.ram,
          storage: selectedPlan.storage,
          bandwidth: selectedPlan.traffic || selectedPlan.bandwidth,
          // Frontend-specific fields for display
          frontendConfig: {
            operatingSystem: selectedOS,
            location: selectedLocation,
            additionalIPs: additionalIPs,
            backup: isAutoBackup || backupEnabled,
            planName: selectedPlan.name,
            autoBackupOption: autoBackupOption,
            privateNetworking: privateNetworking,
            ipv4Addresses: ipv4Addresses,
            objectStorage: objectStorage,
            serverManagement: serverManagement,
            monitoring: monitoring,
            ssl: ssl
          }
        }
      };

      console.log("Adding VPS to cart:", cartData);
      console.log("Selected plan:", selectedPlan);

      // Ajouter au panier via le service
      const response = await cartService.addItemToCart(cartData);

      console.log("Cart response:", response);

      // Mettre à jour le compteur du panier
      if (response.data?.cart?.cartCount) {
        setCartCount(response.data.cart.cartCount);
      }

      // Afficher le message de succès
      toast.success(`${selectedPlan.name} ajouté au panier avec succès!`);

      // Rediriger vers le panier
      router.push('/client/cart');

    } catch (error) {
      console.error("Error adding VPS to cart:", error);
      console.error("Error response:", error.response);
      console.error("Error data:", error.response?.data);

      // Gestion des erreurs spécifiques
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.response?.status === 404) {
        toast.error("Package VPS non trouvé. Veuillez contacter le support.");
      } else if (error.response?.status === 401) {
        toast.error("Veuillez vous connecter pour ajouter au panier.");
        router.push('/auth/login');
      } else if (error.response?.status === 400) {
        toast.error("Données invalides. Veuillez vérifier votre sélection.");
      } else {
        toast.error("Erreur lors de l'ajout au panier. Veuillez réessayer.");
      }
    } finally {
      setOrderLoading(false);
    }
  };

  // Use dynamic OS images data with fallback to static data
  const operatingSystems = osImages.length > 0 ? osImages.map(os => ({
    id: os.id,
    name: os.name,
    icon: getOSIcon(os.type || os.osType),
    type: os.type || os.osType || 'linux',
    description: os.description,
    version: os.version,
    provider: os.provider
  })) : [
    { id: "ubuntu-20.04", name: "Ubuntu 20.04 LTS", icon: UbuntuIcon, type: "linux" },
    { id: "ubuntu-22.04", name: "Ubuntu 22.04 LTS", icon: UbuntuIcon, type: "linux" },
    { id: "centos-8", name: "CentOS 8", icon: CentOSIcon, type: "linux" },
    { id: "debian-11", name: "Debian 11", icon: DebianIcon, type: "linux" }
  ];

  // Helper function to get OS icon based on type
  function getOSIcon(osType) {
    const iconMap = {
      'ubuntu': UbuntuIcon,
      'centos': CentOSIcon,
      'debian': DebianIcon,
      'windows': WindowsIcon,
      'linux': UbuntuIcon // default for linux
    };

    // Check if osType contains specific OS names
    if (osType && typeof osType === 'string') {
      const lowerType = osType.toLowerCase();
      if (lowerType.includes('ubuntu')) return UbuntuIcon;
      if (lowerType.includes('centos')) return CentOSIcon;
      if (lowerType.includes('debian')) return DebianIcon;
      if (lowerType.includes('windows')) return WindowsIcon;
    }

    return iconMap[osType] || UbuntuIcon;
  }

  // Use dynamic regions data with fallback to static data
  const locations = regions.length > 0 ? regions.map(region => ({
    id: region.id,
    name: region.name,
    flag: getRegionFlag(region.country),
    ping: getRegionPing(region.id),
    description: region.description,
    city: region.city,
    country: region.country
  })) : [
    { id: "EU", name: "European Union", flag: "🇩🇪", ping: "15ms", description: "Germany", city: "Nuremberg", country: "Germany" },
    { id: "US-central", name: "United States Central", flag: "🇺🇸", ping: "120ms", description: "St. Louis", city: "St. Louis", country: "United States" },
    { id: "SG", name: "Asia Pacific", flag: "🇸🇬", ping: "200ms", description: "Singapore", city: "Singapore", country: "Singapore" }
  ];

  // Helper functions for region display
  function getRegionFlag(country) {
    const flagMap = {
      'Germany': '🇩🇪',
      'United States': '🇺🇸',
      'Singapore': '🇸🇬',
      'France': '🇫🇷',
      'Netherlands': '🇳🇱'
    };
    return flagMap[country] || '🌍';
  }

  function getRegionPing(regionId) {
    const pingMap = {
      'EU': '15ms',
      'US-central': '120ms',
      'US-east': '110ms',
      'US-west': '130ms',
      'SG': '200ms'
    };
    return pingMap[regionId] || '50ms';
  }



  const calculateTotal = () => {
    if (!selectedPlan) return 0;
    let total = selectedPlan.price;

    // Additional IPs cost
    total += additionalIPs * 15; // 15 MAD per additional IP

    // Backup cost (only if not auto backup plan)
    if (!isAutoBackup && backupEnabled) {
      total += 20; // 20 MAD for backup
    }

    // Auto Backup cost (Contabo style)
    if (autoBackupOption === "auto") {
      total += 18; // €1.79 ≈ 18 MAD per month
    }

    // Private Networking cost
    if (privateNetworking === "enabled") {
      total += 28; // 28 MAD per month
    }

    // IPv4 additional addresses cost
    if (ipv4Addresses === 2) {
      total += 42; // 42 MAD per additional IP
    }

    // Object Storage cost
    const objectStorageCosts = {
      "250gb": 50,
      "500gb": 95,
      "750gb": 140,
      "1tb": 180
    };
    if (objectStorage !== "none" && objectStorageCosts[objectStorage]) {
      total += objectStorageCosts[objectStorage];
    }

    // Server Management cost
    if (serverManagement === "managed") {
      total += 1340; // 1340 MAD per month for managed
    }

    // Monitoring cost
    if (monitoring === "full") {
      total += 140; // 140 MAD per month for full monitoring
    }

    // SSL cost (one-time charges, but we'll add monthly equivalent)
    const sslCosts = {
      "basic": 76, // 914 MAD / 12 months ≈ 76 MAD per month
      "wildcard": 228 // 2740 MAD / 12 months ≈ 228 MAD per month
    };
    if (ssl !== "none" && sslCosts[ssl]) {
      total += sslCosts[ssl];
    }

    // Apply quantity
    total *= quantity;

    // Period multiplier
    const multipliers = {
      monthly: 1,
      '6months': 6 * 0.97, // 3% discount
      annually: 12 * 0.90   // 10% discount
    };

    return total * multipliers[selectedPeriod];
  };



  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <Typography className="text-gray-600">
            Loading VPS configuration...
          </Typography>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Typography variant="h6" className="text-red-600 mb-2">
            Error loading VPS configuration
          </Typography>
          <Typography className="text-gray-600 mb-4">
            {error}
          </Typography>
          <Button
            onClick={() => window.location.reload()}
            color="blue"
            size="sm"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Mobile Optimized */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
            <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto">
              <Button
                variant="outlined"
                size="sm"
                onClick={() => window.history.back()}
                className="border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0"
              >
                <ArrowLeftIcon className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">{t('back')}</span>
              </Button>
              <div className="min-w-0 flex-1">
                <Typography variant="h4" className="text-lg sm:text-2xl text-gray-900 font-bold truncate">
                  {t('page_title')}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 truncate">
                  {selectedPlan?.name || 'Loading...'}
                </Typography>
              </div>
            </div>
            <div className="text-left sm:text-right w-full sm:w-auto flex-shrink-0">
              <Typography className="text-xs sm:text-sm text-gray-500">{t('price_from')}</Typography>
              <Typography variant="h3" className="text-lg sm:text-2xl text-blue-600 font-bold">
                {selectedPlan?.price || 0} MAD{t('per_month')}
              </Typography>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Configuration Panel - Mobile Optimized */}
          <div className="lg:col-span-2 space-y-6 sm:space-y-8">

            {/* Plan Summary - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                  <div className="w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                    <ServerIcon className="w-5 sm:w-6 h-5 sm:h-6 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <Typography variant="h5" className="text-lg sm:text-xl text-gray-900 font-bold truncate">
                      {selectedPlan?.name || 'Loading...'}
                    </Typography>
                    {isAutoBackup && (
                      <div className="flex items-center gap-2 mt-1">
                        <ShieldIcon className="w-3 sm:w-4 h-3 sm:h-4 text-green-600" />
                        <span className="text-xs sm:text-sm text-green-600 font-medium">Auto Backup Inclus</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <CpuIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">{t('vcpu_cores')}</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan?.cores || 0}</div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2"></div>
                    <div className="text-xs sm:text-sm text-gray-600">{t('ram')}</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan?.ram || '0 GB'}</div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <HardDriveIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">{t('storage')}</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan?.storage || '0 GB'}</div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <GlobeIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">{t('traffic')}</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan?.traffic || '0 TB'}</div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Billing Period - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  1. {t('billing_period')}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('billing_period_desc')}
                </Typography>
                <div className="space-y-3">
                  {[
                    { id: 'monthly', label: '1 mois', discount: '', price: selectedPlan?.price || 0 },
                    { id: '6months', label: '6 mois', discount: '3% de réduction', price: Math.round((selectedPlan?.price || 0) * 6 * 0.97) },
                    { id: 'annually', label: '12 mois', discount: '10% de réduction', price: Math.round((selectedPlan?.price || 0) * 12 * 0.90) }
                  ].map((period) => (
                    <div
                      key={period.id}
                      onClick={() => setSelectedPeriod(period.id)}
                      className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedPeriod === period.id
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                          <div className={`w-4 h-4 rounded-full border-2 flex-shrink-0 ${
                            selectedPeriod === period.id ? 'border-blue-600 bg-blue-600' : 'border-gray-300'
                          }`}>
                            {selectedPeriod === period.id && (
                              <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                            )}
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="font-medium text-sm sm:text-base text-gray-900">{period.label}</div>
                            {period.discount && (
                              <div className="text-xs sm:text-sm text-green-600 font-medium">{period.discount}</div>
                            )}
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0">
                          <div className="font-bold text-sm sm:text-base text-gray-900">{period.price} MAD</div>
                          <div className="text-xs sm:text-sm text-gray-500">
                            {period.id === 'monthly' ? '/mois' : period.id === '6months' ? '/6 mois' : '/an'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Operating System Selection - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  2. {t('choose_os')}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('choose_os_desc')}
                </Typography>
                {osImages.length === 0 ? (
                  <div className="text-center py-4">
                    <div className="text-gray-500">🔄 Chargement des systèmes d'exploitation...</div>
                    <div className="text-xs text-gray-400 mt-1">({operatingSystems.length} OS disponibles en fallback)</div>
                    <button
                      onClick={async () => {
                        console.log("🔄 Force fetching OS images...");
                        try {
                          const response = await vpsService.getImages('contabo');
                          console.log("✅ Force fetch result:", response);
                        } catch (error) {
                          console.error("❌ Force fetch error:", error);
                        }
                      }}
                      className="mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm"
                    >
                      🔄 Forcer le chargement des OS
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-2">
                    <div className="text-green-600 text-sm">✅ {osImages.length} systèmes d'exploitation chargés depuis l'API</div>
                  </div>
                )}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {operatingSystems.map((os) => (
                    <div
                      key={os.id}
                      onClick={() => setSelectedOS(os.id)}
                      className={`relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedOS === os.id
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-3 sm:gap-4">
                        <os.icon className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-sm sm:text-base text-gray-900 break-words">{os.name}</div>
                          <div className="text-xs sm:text-sm text-gray-500 mt-1">
                            {os.type === 'linux' ? 'Linux Distribution' : 'Windows Server'}
                          </div>
                        </div>
                        {selectedOS === os.id && (
                          <CheckIcon className="w-5 h-5 text-blue-600 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Location Selection - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  3. {t('choose_location')}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('choose_location_desc')}
                </Typography>
                {regions.length === 0 ? (
                  <div className="text-center py-4">
                    <div className="text-gray-500">🔄 Chargement des emplacements...</div>
                    <div className="text-xs text-gray-400 mt-1">({locations.length} emplacements disponibles en fallback)</div>
                    <button
                      onClick={async () => {
                        console.log("🔄 Force fetching regions...");
                        try {
                          const response = await vpsService.getRegions('contabo');
                          console.log("✅ Force fetch regions result:", response);
                        } catch (error) {
                          console.error("❌ Force fetch regions error:", error);
                        }
                      }}
                      className="mt-2 px-4 py-2 bg-green-500 text-white rounded text-sm"
                    >
                      🔄 Forcer le chargement des régions
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-2">
                    <div className="text-green-600 text-sm">✅ {regions.length} emplacements chargés depuis l'API</div>
                  </div>
                )}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {locations.map((location) => (
                    <div
                      key={location.id}
                      onClick={() => setSelectedLocation(location.id)}
                      className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedLocation === location.id
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                          <span className="text-xl sm:text-2xl flex-shrink-0">{location.id}</span>
                          <div className="min-w-0 flex-1">
                            <div className="font-medium text-sm sm:text-base text-gray-900 truncate">{location.name}</div>
                          </div>
                        </div>
                        {selectedLocation === location.id && (
                          <CheckIcon className="w-4 sm:w-5 h-4 sm:h-5 text-blue-600 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* 4. Data Protection with Auto Backup - Improved Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  4. Data Protection with Auto Backup
                </Typography>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Auto Backup Option */}
                  <div
                    onClick={() => setAutoBackupOption("auto")}
                    className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      autoBackupOption === "auto"
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-blue-300 hover:shadow-sm'
                    }`}
                  >
                    {autoBackupOption === "auto" && (
                      <div className="absolute top-4 right-4">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full inline-block mb-3">
                        Notre Recommandation
                      </div>
                      <div className="font-bold text-xl text-gray-900 mb-2">Auto Backup</div>
                      <div className="text-blue-600 font-bold text-lg mb-3">18 MAD/mois</div>
                      <div className="text-gray-600 mb-2 font-medium">Set it and forget it.</div>
                      <div className="text-gray-500 text-sm mb-4">Data security with no effort</div>

                      <div className="bg-white rounded-lg p-4 space-y-3 text-sm border">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Mode</span>
                          <span className="font-semibold text-gray-900">automated</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Frequency</span>
                          <span className="font-semibold text-gray-900">daily</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Recovery</span>
                          <span className="font-semibold text-gray-900">1-Click Recovery</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Backup Retention</span>
                          <span className="font-semibold text-gray-900">10 days</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* None Option */}
                  <div
                    onClick={() => setAutoBackupOption("none")}
                    className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      autoBackupOption === "none"
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-blue-300 hover:shadow-sm'
                    }`}
                  >
                    {autoBackupOption === "none" && (
                      <div className="absolute top-4 right-4">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="font-bold text-xl text-gray-900 mb-2 mt-8">None</div>
                      <div className="text-green-600 font-bold text-lg mb-6">Free</div>

                      <div className="bg-white rounded-lg p-4 space-y-3 text-sm border">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Mode</span>
                          <span className="font-semibold text-gray-900">manual</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Frequency</span>
                          <span className="font-semibold text-gray-900">on demand</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Recovery</span>
                          <span className="font-semibold text-gray-900">manual</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Backup Retention</span>
                          <span className="font-semibold text-gray-900">-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* 5. Networking - Contabo Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  5. Networking
                </Typography>

                <div className="space-y-4">
                  {/* Private Networking */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">Private Networking</span>
                    <div className="flex items-center gap-3">
                      <select
                        value={privateNetworking}
                        onChange={(e) => setPrivateNetworking(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">No Private Networking</option>
                        <option value="enabled">Private Networking Enabled</option>
                      </select>
                      <div className="min-w-[60px] text-right">
                        {privateNetworking === "enabled" ? (
                          <span className="text-sm sm:text-base font-bold text-gray-900">28 MAD</span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-green-600">Free</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Bandwidth */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">Bandwidth</span>
                    <div className="text-right">
                      <div className="font-medium text-sm sm:text-base text-gray-900">32 TB Out + Unlimited In</div>
                      <div className="text-xs sm:text-sm text-purple-600">200 Mbit/s Connection</div>
                    </div>
                  </div>

                  {/* IPv4 */}
                  <div className="flex items-center justify-between py-2">
                    <span className="font-medium text-sm sm:text-base text-gray-900">IPv4</span>
                    <div className="flex items-center gap-3">
                      <select
                        value={ipv4Addresses}
                        onChange={(e) => setIpv4Addresses(parseInt(e.target.value))}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value={1}>1 IP Address</option>
                        <option value={2}>1 IP Address + 1 Additional IP</option>
                      </select>
                      <div className="min-w-[60px] text-right">
                        {ipv4Addresses === 2 ? (
                          <span className="text-sm sm:text-base font-bold text-gray-900">42 MAD</span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-green-600">Free</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* 6. Add-Ons - Contabo Style */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  6. Add-Ons
                </Typography>

                <div className="space-y-4">
                  {/* Object Storage */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">Object Storage</span>
                    <div className="flex items-center gap-3">
                      <select
                        value={objectStorage}
                        onChange={(e) => setObjectStorage(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">None</option>
                        <option value="250gb">250 GB Object Storage</option>
                        <option value="500gb">500 GB Object Storage</option>
                        <option value="750gb">750 GB Object Storage</option>
                        <option value="1tb">1 TB Object Storage</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {objectStorage === "250gb" && <span className="text-sm sm:text-base font-bold text-gray-900">50 MAD</span>}
                        {objectStorage === "500gb" && <span className="text-sm sm:text-base font-bold text-gray-900">95 MAD</span>}
                        {objectStorage === "750gb" && <span className="text-sm sm:text-base font-bold text-gray-900">140 MAD</span>}
                        {objectStorage === "1tb" && <span className="text-sm sm:text-base font-bold text-gray-900">180 MAD</span>}
                        {objectStorage === "none" && <span className="text-sm sm:text-base font-bold text-green-600">Free</span>}
                      </div>
                    </div>
                  </div>

                  {/* Server Management */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">Server Management</span>
                    <div className="flex items-center gap-3">
                      <select
                        value={serverManagement}
                        onChange={(e) => setServerManagement(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="unmanaged">Unmanaged</option>
                        <option value="managed">Managed</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {serverManagement === "unmanaged" ? (
                          <span className="text-sm sm:text-base font-bold text-green-600">Free</span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-gray-900">1340 MAD</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Monitoring */}
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-sm sm:text-base text-gray-900">Monitoring</span>
                    <div className="flex items-center gap-3">
                      <select
                        value={monitoring}
                        onChange={(e) => setMonitoring(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">None</option>
                        <option value="full">Full Monitoring</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {monitoring === "full" ? (
                          <span className="text-sm sm:text-base font-bold text-gray-900">140 MAD</span>
                        ) : (
                          <span className="text-sm sm:text-base font-bold text-green-600">Free</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* SSL */}
                  <div className="flex items-center justify-between py-2">
                    <span className="font-medium text-sm sm:text-base text-gray-900">SSL</span>
                    <div className="flex items-center gap-3">
                      <select
                        value={ssl}
                        onChange={(e) => setSsl(e.target.value)}
                        className="border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base"
                      >
                        <option value="none">None</option>
                        <option value="basic">SSL certificate</option>
                        <option value="wildcard">SSL certificate (wildcard)</option>
                      </select>
                      <div className="min-w-[80px] text-right">
                        {ssl === "basic" && (
                          <div className="text-right">
                            <div className="text-sm sm:text-base font-bold text-gray-900">914 MAD</div>
                            <div className="text-xs sm:text-sm text-gray-500">One off charge</div>
                          </div>
                        )}
                        {ssl === "wildcard" && (
                          <div className="text-right">
                            <div className="text-sm sm:text-base font-bold text-gray-900">2740 MAD</div>
                            <div className="text-xs sm:text-sm text-gray-500">One off charge</div>
                          </div>
                        )}
                        {ssl === "none" && <span className="text-sm sm:text-base font-bold text-green-600">Free</span>}
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Login & Password Section */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-700 font-semibold mb-6">
                  8. Login & password for your server
                </Typography>

                <div className="space-y-6">
                  {/* Username */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                    <label className="font-medium text-sm sm:text-base text-gray-900 min-w-[100px]">
                      Username
                    </label>
                    <span className="text-gray-700 bg-gray-50 px-3 py-2 rounded border">
                      root
                    </span>
                  </div>

                  {/* Password */}
                  <div className="flex flex-col gap-2">
                    <div className="flex flex-col sm:flex-row sm:items-start gap-2 sm:gap-4">
                      <label className="font-medium text-sm sm:text-base text-gray-900 min-w-[100px] sm:mt-2">
                        Password
                      </label>
                      <div className="flex-1 space-y-2">
                        <button
                          type="button"
                          onClick={() => {/* Add generate password logic */}}
                          className="text-blue-600 hover:text-blue-800 text-sm underline mb-2"
                        >
                          Generate new password
                        </button>
                        <div className="relative">
                          <input
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={handlePasswordChange}
                            placeholder="Enter a secure password"
                            className={`w-full border rounded px-3 py-2 pr-12 text-sm sm:text-base ${
                              passwordError ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
                            }`}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showPassword ? (
                              <EyeOffIcon className="w-4 h-4" />
                            ) : (
                              <EyeIcon className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    {passwordError && (
                      <div className="flex items-center gap-2 text-red-600 text-sm ml-0 sm:ml-[116px]">
                        <span className="w-4 h-4 rounded-full bg-red-600 text-white flex items-center justify-center text-xs font-bold">!</span>
                        Please enter a valid password
                      </div>
                    )}
                    
                    <div className="text-gray-600 text-sm ml-0 sm:ml-[116px] mt-2">
                      <p>
                        In order to use SSH Keys you can add them in the Customer Control Panel later. 
                        Your password will not be sent via email. Be sure to remember it for Windows access. 
                        If you forget the password, you will need to reinstall your server.
                      </p>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

          </div>

          {/* Order Summary - Fixed Position */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-4 lg:max-h-[calc(100vh-2rem)] lg:overflow-hidden">
              <Card className="shadow-lg h-full flex flex-col">
              <CardBody className="p-4 sm:p-6 flex flex-col h-full">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6 flex-shrink-0">
                  {t('order_summary')}
                </Typography>

                <div className="flex-1 overflow-y-auto">

                <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                  <div className="flex justify-between items-start">
                    <div className="min-w-0 flex-1 pr-2">
                      <span className="text-sm sm:text-base text-gray-600">{selectedPlan?.name || 'Loading...'}</span>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">Quantité:</span>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="outlined"
                            onClick={() => setQuantity(Math.max(1, quantity - 1))}
                            disabled={quantity === 1}
                            className="w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0"
                          >
                            -
                          </Button>
                          <span className="w-8 text-center font-medium text-xs">{quantity}</span>
                          <Button
                            size="sm"
                            variant="outlined"
                            onClick={() => setQuantity(Math.min(10, quantity + 1))}
                            disabled={quantity === 10}
                            className="w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0"
                          >
                            +
                          </Button>
                        </div>
                      </div>
                    </div>
                    <span className="font-medium text-sm sm:text-base flex-shrink-0">{(selectedPlan?.price || 0) * quantity} MAD</span>
                  </div>

                  {additionalIPs > 0 && (
                    <div className="flex justify-between items-start">
                      <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">IPs additionnelles ({additionalIPs} × {quantity})</span>
                      <span className="font-medium text-sm sm:text-base flex-shrink-0">{additionalIPs * 15 * quantity} MAD</span>
                    </div>
                  )}

                  {!isAutoBackup && backupEnabled && (
                    <div className="flex justify-between items-start">
                      <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">Sauvegarde automatique × {quantity}</span>
                      <span className="font-medium text-sm sm:text-base flex-shrink-0">{20 * quantity} MAD</span>
                    </div>
                  )}

                  {selectedPeriod !== 'monthly' && (
                    <div className="flex justify-between items-start text-green-600">
                      <span className="text-sm sm:text-base min-w-0 flex-1 pr-2">Réduction ({selectedPeriod === '6months' ? '3%' : '10%'})</span>
                      <span className="text-sm sm:text-base flex-shrink-0">-{Math.round((selectedPlan?.price || 0) * quantity * (selectedPeriod === '6months' ? 6 * 0.03 : 12 * 0.10))} MAD</span>
                    </div>
                  )}

                  <hr className="border-gray-200" />

                  <div className="flex justify-between items-start text-base sm:text-lg font-bold">
                    <span className="min-w-0 flex-1 pr-2">{t('total')}</span>
                    <span className="text-blue-600 flex-shrink-0 text-right">{Math.round(calculateTotal())} MAD/{selectedPeriod === 'monthly' ? 'mois' : selectedPeriod === '6months' ? '6 mois' : 'an'}</span>
                  </div>

                  {/* Configuration Summary - Mobile Optimized */}
                  <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200">
                    <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                      <div className="flex flex-wrap"><strong>{t('os_label')}:</strong>&nbsp;<span className="break-all">{operatingSystems.find(os => os.id === selectedOS)?.name}</span></div>
                      <div className="flex flex-wrap"><strong>{t('location_label')}:</strong>&nbsp;<span className="break-all">{locations.find(loc => loc.id === selectedLocation)?.name}</span></div>
                      <div className="flex flex-wrap"><strong>Période:</strong>&nbsp;<span className="break-all">{selectedPeriod === 'monthly' ? '1 mois' : selectedPeriod === '6months' ? '6 mois' : '12 mois'}</span></div>
                    </div>
                  </div>
                </div>

                <Button
                  size="lg"
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold"
                  onClick={handleAddToCart}
                  disabled={orderLoading || loading || !selectedPlan}
                >
                  {orderLoading ? "Ajout en cours..." : "Ajouter au panier"}
                </Button>

                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2">
                    <ShieldIcon className="w-3 sm:w-4 h-3 sm:h-4" />
                    <span>Paiement sécurisé</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500">
                    <ClockIcon className="w-3 sm:w-4 h-3 sm:h-4" />
                    <span>Déploiement en 5 minutes</span>
                  </div>
                </div>

                </div>
              </CardBody>
            </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
