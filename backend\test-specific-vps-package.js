const axios = require('axios');

async function testSpecificVPSPackage() {
  try {
    console.log('🔍 Testing specific VPS package (V91)...\n');

    // Test 1: Get all packages and filter for VPS
    console.log('1. Checking all packages for VPS configuration...');
    try {
      const packagesResponse = await axios.get('http://localhost:5002/package/get-packages');
      const allPackages = packagesResponse.data.data || [];
      
      console.log(`   Total packages found: ${allPackages.length}`);
      
      // Check for VPS packages
      const vpsPackages = allPackages.filter(pkg => 
        pkg.vpsConfig && pkg.vpsConfig.provider
      );
      
      console.log(`   VPS packages found: ${vpsPackages.length}`);
      
      if (vpsPackages.length > 0) {
        console.log('   VPS Packages details:');
        vpsPackages.forEach((pkg, index) => {
          console.log(`   ${index + 1}. Name: ${pkg.name}`);
          console.log(`      ID: ${pkg._id}`);
          console.log(`      Price: ${pkg.price} MAD`);
          console.log(`      Provider: ${pkg.vpsConfig.provider}`);
          console.log(`      Product ID: ${pkg.vpsConfig.providerProductId}`);
          console.log(`      Active: ${pkg.isActive}`);
          console.log('');
        });
        
        // Test with the first VPS package found
        const testPackage = vpsPackages[0];
        await testVPSOrderWithPackage(testPackage);
        
      } else {
        console.log('   ❌ No VPS packages found with vpsConfig');
        
        // Check if there are packages with V91 in the name or description
        const v91Packages = allPackages.filter(pkg => 
          pkg.name?.includes('V91') || 
          pkg.description?.includes('V91') ||
          JSON.stringify(pkg).includes('V91')
        );
        
        if (v91Packages.length > 0) {
          console.log('   📋 Found packages mentioning V91:');
          v91Packages.forEach((pkg, index) => {
            console.log(`   ${index + 1}. ${pkg.name} - ${pkg.price} MAD`);
            console.log(`      vpsConfig: ${JSON.stringify(pkg.vpsConfig || 'NOT SET')}`);
          });
        }
      }
      
    } catch (error) {
      console.log('❌ Failed to get packages');
      console.log('   Error:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testVPSOrderWithPackage(vpsPackage) {
  console.log(`\n2. Testing VPS order creation with package: ${vpsPackage.name}`);
  
  const orderData = {
    packageId: vpsPackage._id,
    region: "EU",
    operatingSystem: "ubuntu-22.04",
    displayName: "test-vps-server",
    billingCycle: "monthly",
    billingInfo: {
      name: "Test User",
      email: "<EMAIL>",
      phone: "+1234567890",
      address: "123 Test Street",
      country: "US",
      isCompany: false,
    },
    sshKeys: [],
  };

  try {
    const orderResponse = await axios.post(
      "http://localhost:5002/vps/order",
      orderData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MjIwMmYyOTkyZDE2Y2ZiYzQ2NDAwOCIsIm5hbWUiOiJSw6hkYSIsImVtYWlsIjoidHJvdWtpMjEzQGdtYWlsLmNvbSIsInBob3RvIjoiaHR0cHM6Ly9saDMuZ29vZ2xldXNlcmNvbnRlbnQuY29tL2EvQUNnOG9jSjdTOFBvZFppdllITEt4Vng0THZsY1dJazNJcktLX2xkbDFrdkJMNkM3X2xXZFVETmI9czk2LWMiLCJyb2xlIjoiQURNSU4iLCJpYXQiOjE3NTMwOTYzMTYsImV4cCI6MTc1MzE4MjcxNn0.X6AJd51469PrGehvKXLgQgIqxH-wgwOmv1yfcry02yQ'
        }
      }
    );
    
    console.log('✅ VPS order created successfully!');
    console.log('   Order ID:', orderResponse.data.data?.orderId);
    console.log('   Status:', orderResponse.data.data?.status);
    
  } catch (error) {
    console.log('❌ VPS order creation failed');
    console.log('   Status:', error.response?.status);
    console.log('   Error:', error.response?.data?.message || error.message);
    
    if (error.response?.data?.errors) {
      console.log('   Validation Errors:');
      error.response.data.errors.forEach((err, index) => {
        console.log(`     ${index + 1}. ${err.key}: ${err.msg}`);
      });
    }
  }
}

// Run the test
testSpecificVPSPackage();
