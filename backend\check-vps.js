const mongoose = require("mongoose");
const Package = require("./models/Package");

mongoose
  .connect(
    "mongodb+srv://jakiezian:<EMAIL>/ztech?retryWrites=true&w=majority&appName=ztech-dev"
  )
  .then(async () => {
    console.log("Connected to MongoDB");
    const vpsPackages = await Package.find({
      "vpsConfig.provider": { $exists: true },
    }).populate("category");
    console.log("VPS Packages found:", vpsPackages.length);
    vpsPackages.forEach((pkg) => {
      console.log(`- ${pkg.name} (ID: ${pkg._id})`);
      console.log(`  Provider: ${pkg.vpsConfig.provider}`);
      console.log(`  Product ID: ${pkg.vpsConfig.providerProductId}`);
      console.log(`  Price: ${pkg.price} MAD`);
      console.log("");
    });
    process.exit(0);
  })
  .catch((err) => {
    console.error("Error:", err);
    process.exit(1);
  });
