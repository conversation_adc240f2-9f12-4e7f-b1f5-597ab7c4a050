/**
 * VPS Provider Factory
 * Factory pattern implementation for creating VPS provider instances
 * Follows Open/Closed Principle (OCP) from SOLID - open for extension, closed for modification
 */

const ContaboProvider = require('./ContaboProvider');

class VPSProviderFactory {
  /**
   * Registry of available providers
   * @private
   */
  static providers = new Map([
    ['contabo', ContaboProvider],
    ['mock-contabo'], // For testing
    // Future providers can be added here without modifying existing code
    // ['digitalocean', DigitalOceanProvider],
    // ['vultr', VultrProvider],
    // ['linode', LinodeProvider],
  ]);

  /**
   * Create a VPS provider instance
   * @param {string} providerName - Name of the provider
   * @param {Object} config - Provider configuration
   * @returns {VPSProviderInterface} Provider instance
   * @throws {Error} If provider is not supported
   */
  static createProvider(providerName, config = {}) {
    const normalizedName = providerName.toLowerCase();
    
    if (!this.providers.has(normalizedName)) {
      throw new Error(`Unsupported VPS provider: ${providerName}. Supported providers: ${Array.from(this.providers.keys()).join(', ')}`);
    }

    const ProviderClass = this.providers.get(normalizedName);
    return new ProviderClass(config);
  }

  /**
   * Register a new provider
   * @param {string} name - Provider name
   * @param {Class} providerClass - Provider class
   */
  static registerProvider(name, providerClass) {
    const normalizedName = name.toLowerCase();
    this.providers.set(normalizedName, providerClass);
  }

  /**
   * Get list of supported providers
   * @returns {Array<string>} Array of provider names
   */
  static getSupportedProviders() {
    return Array.from(this.providers.keys());
  }

  /**
   * Check if a provider is supported
   * @param {string} providerName - Provider name to check
   * @returns {boolean} True if provider is supported
   */
  static isProviderSupported(providerName) {
    return this.providers.has(providerName.toLowerCase());
  }

  /**
   * Create provider from environment configuration
   * @returns {VPSProviderInterface} Provider instance based on environment
   */
  static createFromEnvironment() {
    // Check if we're in testing mode
    const isTestMode = process.env.VPS_TEST_MODE === 'true' || process.env.NODE_ENV === 'test';
    const defaultProvider = isTestMode ? 'mock-contabo' : (process.env.DEFAULT_VPS_PROVIDER || 'contabo');

    console.log(`🔧 VPS Provider Factory: Using provider '${defaultProvider}' (test mode: ${isTestMode})`);
    return this.createProvider(defaultProvider);
  }

  /**
   * Create multiple providers
   * @param {Array<string>} providerNames - Array of provider names
   * @returns {Map<string, VPSProviderInterface>} Map of provider instances
   */
  static createMultipleProviders(providerNames) {
    const providers = new Map();
    
    for (const name of providerNames) {
      try {
        const provider = this.createProvider(name);
        providers.set(name, provider);
      } catch (error) {
        console.warn(`Failed to create provider ${name}:`, error.message);
      }
    }
    
    return providers;
  }

  /**
   * Validate provider configuration
   * @param {string} providerName - Provider name
   * @returns {Promise<boolean>} True if configuration is valid
   */
  static async validateProviderConfig(providerName) {
    try {
      const provider = this.createProvider(providerName);
      return await provider.validateConfig();
    } catch (error) {
      console.error(`Failed to validate ${providerName} configuration:`, error.message);
      return false;
    }
  }

  /**
   * Get provider capabilities
   * @param {string} providerName - Provider name
   * @returns {Object} Provider capabilities
   */
  static getProviderCapabilities(providerName) {
    const capabilities = {
      contabo: {
        regions: ['EU', 'US-EAST', 'US-WEST', 'ASIA'],
        operatingSystems: ['ubuntu', 'debian', 'centos', 'windows'],
        features: ['ssh-keys', 'snapshots', 'backups', 'monitoring'],
        billing: ['hourly', 'monthly'],
        scalable: true
      }
      // Add capabilities for other providers as they are implemented
    };

    return capabilities[providerName.toLowerCase()] || {};
  }
}

module.exports = VPSProviderFactory;
