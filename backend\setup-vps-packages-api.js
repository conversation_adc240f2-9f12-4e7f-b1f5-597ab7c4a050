/**
 * VPS Packages Setup Script
 *
 * This script creates VPS packages in the database via API calls.
 *
 * BEFORE RUNNING:
 * 1. Make sure the backend server is running on port 5002
 * 2. Login to the admin panel in your browser
 * 3. Open browser developer tools (F12)
 * 4. Go to Application/Storage > Cookies > localhost:3000
 * 5. Copy the 'token' cookie value
 * 6. Replace ADMIN_TOKEN below with your current token
 *
 * OR set environment variable: ADMIN_TOKEN=your_token_here
 */

const axios = require("axios");

const BASE_URL = "http://localhost:5002";

// Admin token - you'll need to replace this with a valid admin token
// REPLACE THIS WITH YOUR CURRENT VALID ADMIN TOKEN
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || "YOUR_CURRENT_ADMIN_TOKEN_HERE";

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "Content-Type": "application/json",
        Cookie: `token=${ADMIN_TOKEN}`,
      },
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Request failed: ${method} ${endpoint}`);
    console.error("   Status:", error.response?.status);
    console.error("   Error:", error.response?.data?.message || error.message);
    throw error;
  }
};

// Create VPS category
const createVPSCategory = async () => {
  try {
    console.log("📁 Creating VPS category...");

    // First, check if VPS category already exists
    const categories = await makeRequest("GET", "/admin/categories");
    const existingVPSCategory = categories.data?.find(
      (cat) => cat.name === "VPS"
    );

    if (existingVPSCategory) {
      console.log("✅ VPS category already exists");
      return existingVPSCategory;
    }

    // Create new VPS category
    const categoryData = {
      name: "VPS",
      description: "Virtual Private Server hosting solutions",
      isActive: true,
    };

    const result = await makeRequest(
      "POST",
      "/category/create-category",
      categoryData
    );
    console.log("✅ Created VPS category");
    return result.data;
  } catch (error) {
    console.error("❌ Error creating VPS category:", error.message);
    throw error;
  }
};

// Create Contabo brand
const createContaboBrand = async () => {
  try {
    console.log("🏢 Creating Contabo brand...");

    // Check if Contabo brand already exists
    const brands = await makeRequest("GET", "/brand/get-brands");
    const existingContaboBrand = brands.data?.find(
      (brand) => brand.name === "Contabo"
    );

    if (existingContaboBrand) {
      console.log("✅ Contabo brand already exists");
      return existingContaboBrand;
    }

    // Create new Contabo brand
    const brandData = {
      name: "Contabo",
      description: "High-performance VPS hosting provider",
      isActive: true,
    };

    const result = await makeRequest("POST", "/brand/create-brand", brandData);
    console.log("✅ Created Contabo brand");
    return result.data;
  } catch (error) {
    console.error("❌ Error creating Contabo brand:", error.message);
    // If brand creation fails, we'll continue without it
    return null;
  }
};

// VPS packages data
const vpsPackagesData = [
  {
    name: "VPS 10 NVMe",
    description:
      "Entry-level VPS with NVMe storage - Perfect for small websites and development",
    price: 150,
    vpsConfig: {
      provider: "contabo",
      providerProductId: "V91",
    },
    specifications: [
      { name: "CPU", value: "1 vCPU" },
      { name: "RAM", value: "4 GB RAM" },
      { name: "Storage", value: "75 GB NVMe SSD" },
      { name: "Bandwidth", value: "32 TB Traffic" },
    ],
  },
  {
    name: "VPS 10 SSD",
    description:
      "Entry-level VPS with SSD storage - Reliable performance for small projects",
    price: 150,
    vpsConfig: {
      provider: "contabo",
      providerProductId: "V92",
    },
    specifications: [
      { name: "CPU", value: "1 vCPU" },
      { name: "RAM", value: "4 GB RAM" },
      { name: "Storage", value: "100 GB SSD" },
      { name: "Bandwidth", value: "32 TB Traffic" },
    ],
  },
  {
    name: "VPS 20 NVMe",
    description:
      "Mid-range VPS with NVMe storage - Great for growing applications",
    price: 270,
    vpsConfig: {
      provider: "contabo",
      providerProductId: "V94",
    },
    specifications: [
      { name: "CPU", value: "2 vCPU" },
      { name: "RAM", value: "8 GB RAM" },
      { name: "Storage", value: "150 GB NVMe SSD" },
      { name: "Bandwidth", value: "32 TB Traffic" },
    ],
  },
  {
    name: "VPS 20 SSD",
    description:
      "Mid-range VPS with SSD storage - Balanced performance and storage",
    price: 270,
    vpsConfig: {
      provider: "contabo",
      providerProductId: "V95",
    },
    specifications: [
      { name: "CPU", value: "2 vCPU" },
      { name: "RAM", value: "8 GB RAM" },
      { name: "Storage", value: "200 GB SSD" },
      { name: "Bandwidth", value: "32 TB Traffic" },
    ],
  },
  {
    name: "VPS 30 NVMe",
    description:
      "High-performance VPS with NVMe storage - For demanding applications",
    price: 450,
    vpsConfig: {
      provider: "contabo",
      providerProductId: "V97",
    },
    specifications: [
      { name: "CPU", value: "4 vCPU" },
      { name: "RAM", value: "16 GB RAM" },
      { name: "Storage", value: "300 GB NVMe SSD" },
      { name: "Bandwidth", value: "32 TB Traffic" },
    ],
  },
  {
    name: "VPS 30 SSD",
    description:
      "High-performance VPS with SSD storage - Enterprise-grade reliability",
    price: 450,
    vpsConfig: {
      provider: "contabo",
      providerProductId: "V98",
    },
    specifications: [
      { name: "CPU", value: "4 vCPU" },
      { name: "RAM", value: "16 GB RAM" },
      { name: "Storage", value: "400 GB SSD" },
      { name: "Bandwidth", value: "32 TB Traffic" },
    ],
  },
];

// Create VPS packages
const createVPSPackages = async (category, brand) => {
  try {
    console.log("📦 Creating VPS packages...");

    let createdCount = 0;
    let skippedCount = 0;

    for (const packageData of vpsPackagesData) {
      try {
        // Check if package already exists
        const packages = await makeRequest("GET", "/admin/packages");
        const existingPackage = packages.data?.find(
          (pkg) =>
            pkg.name === packageData.name &&
            pkg.vpsConfig?.providerProductId ===
              packageData.vpsConfig.providerProductId
        );

        if (existingPackage) {
          console.log(
            `⚠️  Package "${packageData.name}" already exists, skipping...`
          );
          skippedCount++;
          continue;
        }

        // Create package data
        const packagePayload = {
          name: packageData.name,
          description: packageData.description,
          price: packageData.price,
          category: category._id,
          brand: brand?._id,
          isActive: true,
          specifications: packageData.specifications,
          vpsConfig: packageData.vpsConfig,
        };

        await makeRequest("POST", "/admin/packages", packagePayload);
        console.log(
          `✅ Created VPS package: ${packageData.name} (${packageData.vpsConfig.providerProductId})`
        );
        createdCount++;
      } catch (error) {
        console.error(
          `❌ Failed to create package "${packageData.name}":`,
          error.message
        );
      }
    }

    console.log(`\n📊 Package creation summary:`);
    console.log(`   ✅ Created: ${createdCount} packages`);
    console.log(`   ⚠️  Skipped: ${skippedCount} packages (already exist)`);
  } catch (error) {
    console.error("❌ Error creating VPS packages:", error.message);
    throw error;
  }
};

// Verify setup
const verifySetup = async () => {
  try {
    console.log("\n🔍 Verifying VPS packages setup...");

    const packages = await makeRequest("GET", "/package/get-packages");
    const vpsPackages =
      packages.data?.filter((pkg) => pkg.vpsConfig && pkg.vpsConfig.provider) ||
      [];

    console.log(`\n📊 Verification Results:`);
    console.log(`   Total VPS packages found: ${vpsPackages.length}`);

    if (vpsPackages.length > 0) {
      console.log(`   VPS Packages:`);
      vpsPackages.forEach((pkg, index) => {
        console.log(
          `   ${index + 1}. ${pkg.name} - ${pkg.price} MAD (${
            pkg.vpsConfig.providerProductId
          })`
        );
      });
    }

    return vpsPackages.length > 0;
  } catch (error) {
    console.error("❌ Error verifying setup:", error.message);
    return false;
  }
};

// Main setup function
const setupVPSPackages = async () => {
  try {
    console.log("🚀 Starting VPS packages setup via API...\n");

    // Test backend connectivity
    console.log("🔌 Testing backend connectivity...");
    await makeRequest("GET", "/");
    console.log("✅ Backend is accessible\n");

    // Create category and brand
    const vpsCategory = await createVPSCategory();
    const contaboBrand = await createContaboBrand();

    // Create VPS packages
    await createVPSPackages(vpsCategory, contaboBrand);

    // Verify setup
    const success = await verifySetup();

    if (success) {
      console.log("\n🎉 VPS packages setup completed successfully!");
      console.log(
        "🎯 You can now test VPS creation through the frontend or API."
      );
    } else {
      console.log(
        "\n⚠️  Setup completed but no VPS packages were found. Please check the logs above."
      );
    }
  } catch (error) {
    console.error("\n❌ Setup failed:", error.message);
    console.log("\n💡 Make sure:");
    console.log("   1. Backend server is running on port 5002");
    console.log("   2. You have admin privileges");
    console.log("   3. The admin token is valid");
  }
};

// Run the setup
if (require.main === module) {
  setupVPSPackages();
}

module.exports = { setupVPSPackages };
