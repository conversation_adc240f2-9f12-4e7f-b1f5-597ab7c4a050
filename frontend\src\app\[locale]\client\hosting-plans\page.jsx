"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, CardBody, <PERSON><PERSON>, Chip } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import PaymentStatusModal from "../../../../components/order/paymentStatusModal";
import { useSearchParams } from "next/navigation";
import orderService from "../../../services/orderService";
import vpsService from "../../../services/vpsService";
import {
  Server,
  Clock,
  CheckCircle2,
  PhoneCall,
  Rocket,
  AlertCircle,
  RotateCcw,
  Play,
  Square,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  Shield,
  Monitor,
  Database,
  Activity,
  Zap,
  RefreshCw,
  Trash2,
  Edit,
  Eye,
  MoreVertical,
} from "lucide-react";
import { OrderStatus } from "../../../config/ConstStatus";
import { getLocalizedContent } from "@/app/helpers/helpers";
import { useIsMobile } from "@/app/hook/useIsMobile";

// Server Status Component
const ServerStatus = ({ status }) => {
  const statusConfig = {
    running: { color: 'green', icon: Activity, text: 'En ligne' },
    stopped: { color: 'red', icon: Square, text: 'Arrêté' }
  };

  const config = statusConfig[status] || statusConfig.stopped;
  const Icon = config.icon;

  return (
    <div className={`flex items-center gap-2 px-2 py-1 rounded-full bg-${config.color}-100`}>
      <Icon className={`w-3 h-3 text-${config.color}-600`} />
      <span className={`text-xs font-medium text-${config.color}-700`}>
        {config.text}
      </span>
    </div>
  );
};

// VPS Manage Dropdown Menu Component
const VPSManageDropdown = ({ server }) => {
  const [isOpen, setIsOpen] = useState(false);

  const menuItems = [
    { 
      icon: '🔧', 
      label: 'Upgrade VPS', 
      action: () => console.log('Upgrade VPS', server.id),
      color: 'text-blue-600',
      bgHover: 'hover:bg-blue-50',
      category: 'management'
    },
    { 
      icon: '🔄', 
      label: 'Move to other Region', 
      action: () => console.log('Move Region', server.id),
      color: 'text-green-600',
      bgHover: 'hover:bg-green-50',
      category: 'management'
    },
    { 
      icon: '➕', 
      label: 'Order Add-On', 
      action: () => console.log('Order Add-On', server.id),
      color: 'text-purple-600',
      bgHover: 'hover:bg-purple-50',
      category: 'management'
    },
    { 
      icon: '🪟', 
      label: 'Order Windows', 
      action: () => console.log('Order Windows', server.id),
      color: 'text-indigo-600',
      bgHover: 'hover:bg-indigo-50',
      category: 'management'
    },
    { 
      icon: '📁', 
      label: 'Upload ISO Image', 
      action: () => console.log('Upload ISO', server.id),
      color: 'text-orange-600',
      bgHover: 'hover:bg-orange-50',
      category: 'system',
      separator: true
    },
    { 
      icon: '🚫', 
      label: "I can't connect to this server", 
      action: () => console.log('Connection Issue', server.id),
      color: 'text-red-600',
      bgHover: 'hover:bg-red-50',
      category: 'support'
    },
    { 
      icon: '🔑', 
      label: 'Password reset', 
      action: () => console.log('Password Reset', server.id),
      color: 'text-yellow-600',
      bgHover: 'hover:bg-yellow-50',
      category: 'support'
    },
    { 
      icon: 'ℹ️', 
      label: 'VNC Information', 
      action: () => console.log('VNC Info', server.id),
      color: 'text-cyan-600',
      bgHover: 'hover:bg-cyan-50',
      category: 'vnc',
      separator: true
    },
    { 
      icon: '🔐', 
      label: 'VNC Password', 
      action: () => console.log('VNC Password', server.id),
      color: 'text-teal-600',
      bgHover: 'hover:bg-teal-50',
      category: 'vnc'
    },
    { 
      icon: '❌', 
      label: 'Disable VNC', 
      action: () => console.log('Disable VNC', server.id),
      color: 'text-red-500',
      bgHover: 'hover:bg-red-50',
      category: 'vnc'
    }
  ];

  return (
    <div className="relative inline-block">
      <Button
        size="sm"
        className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-xs font-medium rounded transition-all duration-200 hover:shadow-md"
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
        onClick={() => window.open(`/client/server-management/${server.id}`, '_blank')}
      >
        Manage
      </Button>

      {isOpen && (
        <div
          className="absolute right-0 top-full mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-50 overflow-hidden animate-in fade-in-0 zoom-in-95 duration-150"
          onMouseEnter={() => setIsOpen(true)}
          onMouseLeave={() => setIsOpen(false)}
          style={{
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
          }}
        >
          <div className="py-1">
            <div className="px-3 py-2 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <p className="text-xs font-semibold text-gray-700 uppercase tracking-wide">
                  Actions Serveur
                </p>
                <div className="ml-auto text-xs text-gray-500 font-medium">
                  ID: {server.id}
                </div>
              </div>
            </div>
            <div className="max-h-72 overflow-y-auto">
              {menuItems.map((item, index) => (
                <div key={index}>
                  {item.separator && (
                    <div className="mx-3 my-1 border-t border-gray-200"></div>
                  )}
                  <button
                    onClick={item.action}
                    className={`w-full px-3 py-2 text-left text-sm transition-all duration-200 flex items-center gap-2 ${item.bgHover} ${item.color} hover:bg-opacity-80 group`}
                  >
                    <span className="text-base group-hover:scale-105 transition-transform duration-200">
                      {item.icon}
                    </span>
                    <span className="group-hover:font-medium transition-all duration-200">
                      {item.label}
                    </span>
                    <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </button>
                </div>
              ))}
            </div>
            <div className="px-3 py-2 border-t border-gray-100 bg-gray-50">
              <p className="text-xs text-gray-500 text-center">
                Actions rapides disponibles
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const OrderSteps = ({ currentStatus }) => {
  const steps = [
    { status: OrderStatus.PENDING, label: "En attente", icon: Clock },
    { status: OrderStatus.PROCESSING, label: "En cours", icon: Rocket },
    { status: OrderStatus.COMPLETED, label: "Terminé", icon: CheckCircle2 },
  ];

  const getStepStatus = (stepStatus) => {
    const statusOrder = [OrderStatus.PENDING, OrderStatus.PROCESSING, OrderStatus.COMPLETED];
    const currentIndex = statusOrder.indexOf(currentStatus);
    const stepIndex = statusOrder.indexOf(stepStatus);

    if (stepIndex < currentIndex) return "completed";
    if (stepIndex === currentIndex) return "current";
    return "upcoming";
  };

  return (
    <div className="flex items-center justify-between">
      {steps.map((step, index) => {
        const status = getStepStatus(step.status);
        const Icon = step.icon;

        return (
          <div key={step.status} className="flex items-center">
            <div className="flex flex-col items-center">
              <div
                className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center ${
                  status === "completed"
                    ? "bg-green-500 text-white"
                    : status === "current"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-400"
                }`}
              >
                <Icon className="w-4 h-4 md:w-5 md:h-5" />
              </div>
              <Typography className="text-xs md:text-sm mt-2 text-center font-medium">
                {step.label}
              </Typography>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`flex-1 h-1 mx-2 md:mx-4 rounded ${
                  status === "completed" ? "bg-green-500" : "bg-gray-200"
                }`}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

const StatusBadge = ({ status }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case OrderStatus.PENDING:
        return { color: "amber", label: "En attente", icon: Clock };
      case OrderStatus.PROCESSING:
        return { color: "blue", label: "En cours", icon: Rocket };
      case OrderStatus.COMPLETED:
        return { color: "green", label: "Terminé", icon: CheckCircle2 };
      case OrderStatus.CANCELLED:
        return { color: "red", label: "Annulé", icon: AlertCircle };
      default:
        return { color: "gray", label: "Inconnu", icon: AlertCircle };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Chip
      value={config.label}
      color={config.color}
      className="text-xs font-medium"
      icon={<Icon className="w-3 h-3" />}
    />
  );
};

export default function HostingOrdersPage() {
  const t = useTranslations("client");
  const [subOrders, setSubOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [activeTab, setActiveTab] = useState('vps');
  const localeActive = useLocale();

  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const item = searchParams.get("item");
  const categoryName = "Hosting";

  // VPS instances data from API
  const [vpsServers, setVpsServers] = useState([]);
  const [vpsLoading, setVpsLoading] = useState(true);
  const [vpsError, setVpsError] = useState(null);

  // Mock data for Dedicated servers
  const [dedicatedServers, setDedicatedServers] = useState([
    {
      id: 'ds-001',
      server: '*************',
      defaultUser: 'root',
      hostSystem: '15432',
      status: 'running'
    },
    {
      id: 'ds-002',
      server: '*************',
      defaultUser: 'admin',
      hostSystem: '28901',
      status: 'stopped'
    }
  ]);

  // Mock data for Shared servers
  const [sharedServers, setSharedServers] = useState([
    {
      id: 'sh-001',
      server: 'shared01.ztech.ma',
      resources: '2GB RAM, 50GB SSD',
      status: 'running',
      price: '99 MAD/mois'
    },
    {
      id: 'sh-002',
      server: 'shared02.ztech.ma',
      resources: '4GB RAM, 100GB SSD',
      status: 'running',
      price: '199 MAD/mois'
    }
  ]);

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setOrderId(item);
      setOpenModal(true);
    }
  }, [status, item]);

  useEffect(() => {
    const getSubOrders = async () => {
      try {
        const res = await orderService.getSubOrdersByCategory(categoryName);
        setSubOrders(res.data.subOrders);
      } catch (error) {
        console.error("Error getting suborders", error);
      } finally {
        setLoading(false);
      }
    };
    getSubOrders();
  }, [categoryName]);

  // Fetch VPS instances
  useEffect(() => {
    const fetchVpsInstances = async () => {
      try {
        setVpsLoading(true);
        setVpsError(null);

        const response = await vpsService.getUserInstances();
        console.log("VPS Instances API Response:", response);

        // Handle different response structures
        let instancesData = [];
        if (response.data && Array.isArray(response.data)) {
          instancesData = response.data;
        } else if (response.data && response.data.instances && Array.isArray(response.data.instances)) {
          instancesData = response.data.instances;
        } else if (Array.isArray(response)) {
          instancesData = response;
        }

        // Transform API response to match UI structure
        const transformedInstances = instancesData.map(instance => ({
          id: instance.id || instance.instanceId,
          server: instance.ipAddress || instance.ip || instance.server || 'N/A',
          defaultUser: instance.defaultUser || instance.user || 'root',
          hostSystem: instance.hostId || instance.hostSystem || instance.id,
          status: instance.status || 'unknown',
          name: instance.name || instance.displayName,
          plan: instance.plan || {},
          specs: instance.specs || {}
        }));

        setVpsServers(transformedInstances);
      } catch (error) {
        console.error("Error fetching VPS instances:", error);
        setVpsError(error.message || "Failed to load VPS instances");
        // Keep empty array on error
        setVpsServers([]);
      } finally {
        setVpsLoading(false);
      }
    };

    fetchVpsInstances();
  }, []);

  // VPS control functions
  const handleVpsAction = async (instanceId, action) => {
    try {
      let response;
      switch (action) {
        case 'start':
          response = await vpsService.startInstance(instanceId);
          break;
        case 'stop':
          response = await vpsService.stopInstance(instanceId);
          break;
        case 'restart':
          response = await vpsService.restartInstance(instanceId);
          break;
        case 'reset-password':
          response = await vpsService.resetInstancePassword(instanceId);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (response.success) {
        // Update local state to reflect the action
        setVpsServers(prev => prev.map(server =>
          server.id === instanceId
            ? { ...server, status: action === 'start' ? 'starting' : action === 'stop' ? 'stopping' : 'restarting' }
            : server
        ));

        // Refresh instances after a delay to get updated status
        setTimeout(() => {
          const fetchVpsInstances = async () => {
            try {
              const response = await vpsService.getUserInstances();

              // Handle different response structures
              let instancesData = [];
              if (response.data && Array.isArray(response.data)) {
                instancesData = response.data;
              } else if (response.data && response.data.instances && Array.isArray(response.data.instances)) {
                instancesData = response.data.instances;
              } else if (Array.isArray(response)) {
                instancesData = response;
              }

              const transformedInstances = instancesData.map(instance => ({
                id: instance.id || instance.instanceId,
                server: instance.ipAddress || instance.ip || instance.server || 'N/A',
                defaultUser: instance.defaultUser || instance.user || 'root',
                hostSystem: instance.hostId || instance.hostSystem || instance.id,
                status: instance.status || 'unknown',
                name: instance.name || instance.displayName,
                plan: instance.plan || {},
                specs: instance.specs || {}
              }));

              setVpsServers(transformedInstances);
            } catch (error) {
              console.error("Error refreshing VPS instances:", error);
            }
          };
          fetchVpsInstances();
        }, 2000);
      }
    } catch (error) {
      console.error(`Error performing ${action} on VPS ${instanceId}:`, error);
      alert(`Failed to ${action} VPS. Please try again.`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <Typography className="text-gray-600">
            {t("hosting.loading")}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[80vh] p-4 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <Typography
            variant="h1"
            className="text-2xl md:text-3xl font-bold text-gray-900 mb-2"
          >
            Gestion des Serveurs
          </Typography>
          <Typography className="text-sm md:text-base text-gray-600">
            Gérez vos serveurs VPS, dédiés et partagés depuis un seul endroit
          </Typography>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 border-b border-gray-200">
            <button
              onClick={() => setActiveTab('vps')}
              className={`px-4 py-3 text-sm font-medium rounded-t-lg sm:rounded-none transition-all duration-200 ${
                activeTab === 'vps'
                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center gap-2">
                <Server className="w-4 h-4" />
                <span>VPS ({vpsServers.length})</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('dedicated')}
              className={`px-4 py-3 text-sm font-medium rounded-t-lg sm:rounded-none transition-all duration-200 ${
                activeTab === 'dedicated'
                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4" />
                <span>Serveurs Dédiés ({dedicatedServers.length})</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('shared')}
              className={`px-4 py-3 text-sm font-medium rounded-t-lg sm:rounded-none transition-all duration-200 ${
                activeTab === 'shared'
                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                <span>Serveurs Partagés ({sharedServers.length})</span>
              </div>
            </button>
          </div>
        </div>

        {/* VPS Tab Content */}
        {activeTab === 'vps' && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {vpsLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <Typography className="text-gray-600 text-sm">
                    Loading VPS instances...
                  </Typography>
                </div>
              </div>
            ) : vpsError ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-center">
                  <Typography variant="h6" className="text-red-600 mb-2 text-sm">
                    Error loading VPS instances
                  </Typography>
                  <Typography className="text-gray-600 mb-4 text-xs">
                    {vpsError}
                  </Typography>
                  <Button
                    onClick={() => window.location.reload()}
                    color="blue"
                    size="sm"
                  >
                    Retry
                  </Button>
                </div>
              </div>
            ) : vpsServers.length > 0 ? (
              <>
                {/* Mobile Cards View */}
                <div className="block lg:hidden">
                  <div className="space-y-4 p-4">
                    {vpsServers.map((server) => (
                      <div key={server.id} className="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">{server.server}</p>
                            <p className="text-sm text-gray-500">ID: {server.id}</p>
                          </div>
                          <div className={`w-3 h-3 rounded-full ${
                            server.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Utilisateur:</span>
                            <p className="font-medium">{server.defaultUser}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Système:</span>
                            <p className="font-medium">{server.hostSystem}</p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 pt-2">
                          <button className="text-green-600 hover:text-green-700 p-1">
                            <RefreshCw className="w-5 h-5" />
                          </button>
                          <button className="text-green-600 hover:text-green-700 p-1">
                            <Play className="w-5 h-5" />
                          </button>
                          <button className="text-red-600 hover:text-red-700 p-1">
                            <Square className="w-5 h-5" />
                          </button>
                          <button className="text-orange-600 hover:text-orange-700 p-1">
                            <Shield className="w-5 h-5" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-700 p-1">
                            <RefreshCw className="w-5 h-5" />
                          </button>
                          <button className="text-yellow-600 hover:text-yellow-700 p-1">
                            <Zap className="w-5 h-5" />
                          </button>
                          <button className="text-indigo-600 hover:text-indigo-700 p-1">
                            <Monitor className="w-5 h-5" />
                          </button>
                        </div>

                        <div className="pt-2 border-t border-gray-200">
                          <VPSManageDropdown server={server} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Desktop Table View - Exact match to the image */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full text-sm table-fixed">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr className="text-xs text-gray-500 uppercase tracking-wider">
                        <th className="px-2 py-3 text-left font-medium w-32">SERVEUR</th>
                        <th className="px-2 py-3 text-left font-medium w-24">DÉFAUT USER</th>
                        <th className="px-2 py-3 text-left font-medium w-20">HOST SYSTEM</th>
                        <th className="px-2 py-3 text-left font-medium w-16">STATUT</th>
                        <th className="px-2 py-3 text-left font-medium w-16">RESTART</th>
                        <th className="px-2 py-3 text-left font-medium w-16">START</th>
                        <th className="px-2 py-3 text-left font-medium w-16">STOP</th>
                        <th className="px-2 py-3 text-left font-medium w-20">CLOUD-INIT</th>
                        <th className="px-2 py-3 text-left font-medium w-20">REINSTALL</th>
                        <th className="px-2 py-3 text-left font-medium w-24">RESCUE SYSTEM</th>
                        <th className="px-2 py-3 text-left font-medium w-20">SNAPSHOTS</th>
                        <th className="px-2 py-3 text-left font-medium w-20">MANAGE</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {vpsServers.map((server) => (
                        <tr key={server.id} className="hover:bg-gray-50 transition-colors duration-150">
                          <td className="px-2 py-3 text-sm font-medium text-gray-900 truncate">
                            {server.server}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.defaultUser}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.hostSystem}
                          </td>
                          <td className="px-2 py-3">
                            <div className={`w-3 h-3 rounded-full ${
                              server.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                            }`}></div>
                          </td>
                          <td className="px-2 py-3">
                            <button
                              className="text-green-600 hover:text-green-700 p-1 disabled:opacity-50 disabled:cursor-not-allowed"
                              onClick={() => handleVpsAction(server.id, 'restart')}
                              disabled={server.status === 'restarting' || server.status === 'starting' || server.status === 'stopping'}
                              title="Restart VPS"
                            >
                              <RefreshCw className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button
                              className="text-green-600 hover:text-green-700 p-1 disabled:opacity-50 disabled:cursor-not-allowed"
                              onClick={() => handleVpsAction(server.id, 'start')}
                              disabled={server.status === 'running' || server.status === 'starting'}
                              title="Start VPS"
                            >
                              <Play className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button
                              className="text-red-600 hover:text-red-700 p-1 disabled:opacity-50 disabled:cursor-not-allowed"
                              onClick={() => handleVpsAction(server.id, 'stop')}
                              disabled={server.status === 'stopped' || server.status === 'stopping'}
                              title="Stop VPS"
                            >
                              <Square className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-orange-600 hover:text-orange-700 p-1">
                              <Shield className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-purple-600 hover:text-purple-700 p-1">
                              <RefreshCw className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-yellow-600 hover:text-yellow-700 p-1">
                              <Zap className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-indigo-600 hover:text-indigo-700 p-1">
                              <Monitor className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <VPSManageDropdown server={server} />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-lg text-gray-600 mb-2">
                  Aucun VPS trouvé
                </Typography>
                <Typography className="text-gray-500">
                  Vous n&apos;avez pas encore de serveurs VPS
                </Typography>
              </div>
            )}
          </div>
        )}

        {/* Dedicated Tab Content */}
        {activeTab === 'dedicated' && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {dedicatedServers.length > 0 ? (
              <>
                {/* Mobile Cards View */}
                <div className="block lg:hidden">
                  <div className="space-y-4 p-4">
                    {dedicatedServers.map((server) => (
                      <div key={server.id} className="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">{server.server}</p>
                            <p className="text-sm text-gray-500">ID: {server.id}</p>
                          </div>
                          <div className={`w-3 h-3 rounded-full ${
                            server.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Utilisateur:</span>
                            <p className="font-medium">{server.defaultUser}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Système:</span>
                            <p className="font-medium">{server.hostSystem}</p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 pt-2">
                          <button className="text-green-600 hover:text-green-700 p-1">
                            <RefreshCw className="w-5 h-5" />
                          </button>
                          <button className="text-green-600 hover:text-green-700 p-1">
                            <Play className="w-5 h-5" />
                          </button>
                          <button className="text-red-600 hover:text-red-700 p-1">
                            <Square className="w-5 h-5" />
                          </button>
                          <button className="text-orange-600 hover:text-orange-700 p-1">
                            <Shield className="w-5 h-5" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-700 p-1">
                            <RefreshCw className="w-5 h-5" />
                          </button>
                          <button className="text-yellow-600 hover:text-yellow-700 p-1">
                            <Zap className="w-5 h-5" />
                          </button>
                          <button className="text-indigo-600 hover:text-indigo-700 p-1">
                            <Monitor className="w-5 h-5" />
                          </button>
                        </div>

                        <div className="pt-2 border-t border-gray-200">
                          <VPSManageDropdown server={server} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Desktop Table View - Exact match to the image */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full text-sm table-fixed">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr className="text-xs text-gray-500 uppercase tracking-wider">
                        <th className="px-2 py-3 text-left font-medium w-32">SERVEUR</th>
                        <th className="px-2 py-3 text-left font-medium w-24">DÉFAUT USER</th>
                        <th className="px-2 py-3 text-left font-medium w-20">HOST SYSTEM</th>
                        <th className="px-2 py-3 text-left font-medium w-16">STATUT</th>
                        <th className="px-2 py-3 text-left font-medium w-16">RESTART</th>
                        <th className="px-2 py-3 text-left font-medium w-16">START</th>
                        <th className="px-2 py-3 text-left font-medium w-16">STOP</th>
                        <th className="px-2 py-3 text-left font-medium w-20">CLOUD-INIT</th>
                        <th className="px-2 py-3 text-left font-medium w-20">REINSTALL</th>
                        <th className="px-2 py-3 text-left font-medium w-24">RESCUE SYSTEM</th>
                        <th className="px-2 py-3 text-left font-medium w-20">SNAPSHOTS</th>
                        <th className="px-2 py-3 text-left font-medium w-20">MANAGE</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {dedicatedServers.map((server) => (
                        <tr key={server.id} className="hover:bg-gray-50 transition-colors duration-150">
                          <td className="px-2 py-3 text-sm font-medium text-gray-900 truncate">
                            {server.server}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.defaultUser}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.hostSystem}
                          </td>
                          <td className="px-2 py-3">
                            <div className={`w-3 h-3 rounded-full ${
                              server.status === 'running' ? 'bg-green-500' : 'bg-red-500'
                            }`}></div>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-green-600 hover:text-green-700 p-1">
                              <RefreshCw className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-green-600 hover:text-green-700 p-1">
                              <Play className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-red-600 hover:text-red-700 p-1">
                              <Square className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-orange-600 hover:text-orange-700 p-1">
                              <Shield className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-purple-600 hover:text-purple-700 p-1">
                              <RefreshCw className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-yellow-600 hover:text-yellow-700 p-1">
                              <Zap className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-indigo-600 hover:text-indigo-700 p-1">
                              <Monitor className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <VPSManageDropdown server={server} />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <HardDrive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-lg text-gray-600 mb-2">
                  Aucun serveur dédié trouvé
                </Typography>
                <Typography className="text-gray-500">
                  Vous n&apos;avez pas encore de serveurs dédiés
                </Typography>
              </div>
            )}
          </div>
        )}

        {/* Shared Tab Content */}
        {activeTab === 'shared' && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {sharedServers.length > 0 ? (
              <>
                {/* Mobile Cards View */}
                <div className="block md:hidden">
                  <div className="space-y-4 p-4">
                    {sharedServers.map((server) => (
                      <div key={server.id} className="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">{server.server}</p>
                            <p className="text-sm text-gray-500">ID: {server.id}</p>
                          </div>
                          <ServerStatus status={server.status} />
                        </div>

                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="text-gray-500">Ressources:</span>
                            <p className="font-medium">{server.resources}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Prix:</span>
                            <p className="font-medium text-green-600">{server.price}</p>
                          </div>
                        </div>

                        <div className="pt-2 border-t border-gray-200">
                          <VPSManageDropdown server={server} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Desktop Table View */}
                <div className="hidden md:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Serveur
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Statut
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ressources
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Prix
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sharedServers.map((server) => (
                        <tr key={server.id} className="hover:bg-gray-50 transition-colors duration-150">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{server.server}</div>
                              <div className="text-sm text-gray-500">ID: {server.id}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <ServerStatus status={server.status} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {server.resources}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                            {server.price}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <VPSManageDropdown server={server} />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-lg text-gray-600 mb-2">
                  Aucun serveur partagé trouvé
                </Typography>
                <Typography className="text-gray-500">
                  Vous n&apos;avez pas encore de serveurs partagés
                </Typography>
              </div>
            )}
          </div>
        )}
      </div>

      {openModal && (
        <PaymentStatusModal
          status={paymentStatus}
          orderId={orderId}
          onClose={() => setOpenModal(false)}
        />
      )}
    </div>
  );
}
