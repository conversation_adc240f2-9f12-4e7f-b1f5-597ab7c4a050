const mongoose = require('mongoose');

// Connect and check MongoDB directly
async function checkMongoDBPackages() {
  try {
    console.log('🔍 Checking MongoDB directly for packages...\n');

    // Connect to MongoDB
    const mongoUri = 'mongodb+srv://jakiezian:<EMAIL>/ztech?retryWrites=true&w=majority&appName=ztech-dev';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Get database and collection info
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    console.log('\n📋 Available collections:');
    collections.forEach((col, index) => {
      console.log(`   ${index + 1}. ${col.name}`);
    });

    // Check packages collection
    const packagesCollection = db.collection('packages');
    const packageCount = await packagesCollection.countDocuments();
    console.log(`\n📦 Total documents in 'packages' collection: ${packageCount}`);

    if (packageCount > 0) {
      console.log('\n📋 All packages in database:');
      const allPackages = await packagesCollection.find({}).toArray();
      
      allPackages.forEach((pkg, index) => {
        console.log(`\n   ${index + 1}. Package: ${pkg.name || 'Unnamed'}`);
        console.log(`      ID: ${pkg._id}`);
        console.log(`      Price: ${pkg.price || 'Not set'}`);
        console.log(`      Active: ${pkg.isActive}`);
        console.log(`      VPS Config: ${JSON.stringify(pkg.vpsConfig || 'Not set')}`);
        
        if (pkg.vpsConfig && pkg.vpsConfig.providerProductId === 'V91') {
          console.log('      🎯 THIS IS YOUR V91 PACKAGE!');
        }
      });

      // Check specifically for VPS packages
      const vpsPackages = await packagesCollection.find({ 
        'vpsConfig.provider': { $exists: true } 
      }).toArray();
      
      console.log(`\n🎯 VPS packages (with vpsConfig.provider): ${vpsPackages.length}`);
      
      // Check for V91 specifically
      const v91Packages = await packagesCollection.find({ 
        'vpsConfig.providerProductId': 'V91' 
      }).toArray();
      
      console.log(`🎯 V91 packages: ${v91Packages.length}`);
      
      if (v91Packages.length > 0) {
        console.log('\n✅ Found V91 package(s):');
        v91Packages.forEach((pkg, index) => {
          console.log(`   ${index + 1}. ${pkg.name} - ${pkg.price} MAD`);
          console.log(`      Provider: ${pkg.vpsConfig.provider}`);
          console.log(`      Product ID: ${pkg.vpsConfig.providerProductId}`);
        });
      }

    } else {
      console.log('\n⚠️  No packages found in database');
      
      // Check other possible collection names
      const possibleNames = ['package', 'Package', 'PACKAGES'];
      for (const name of possibleNames) {
        try {
          const altCollection = db.collection(name);
          const altCount = await altCollection.countDocuments();
          if (altCount > 0) {
            console.log(`\n📦 Found ${altCount} documents in '${name}' collection`);
          }
        } catch (error) {
          // Collection doesn't exist, ignore
        }
      }
    }

    // Check categories and brands too
    const categoriesCount = await db.collection('categories').countDocuments();
    const brandsCount = await db.collection('brands').countDocuments();
    
    console.log(`\n📊 Other collections:`);
    console.log(`   Categories: ${categoriesCount}`);
    console.log(`   Brands: ${brandsCount}`);

  } catch (error) {
    console.error('\n❌ Error checking MongoDB:', error.message);
    
    if (error.message.includes('ENOTFOUND')) {
      console.log('\n💡 Network connectivity issue. The package might be in the database but we cannot access it remotely.');
    }
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed.');
    process.exit(0);
  }
}

// Run the check
checkMongoDBPackages();
