/**
 * Simple VPS Packages Creation Script
 * 
 * This script creates VPS packages directly in MongoDB.
 * Run this when you have MongoDB connectivity issues with the API approach.
 * 
 * Usage: node create-vps-packages-simple.js
 */

const mongoose = require('mongoose');

// Simple package schema for direct creation
const packageSchema = new mongoose.Schema({
  name: String,
  description: String,
  price: Number,
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  brand: { type: mongoose.Schema.Types.ObjectId, ref: 'Brand' },
  isActive: { type: Boolean, default: true },
  vpsConfig: {
    provider: String,
    providerProductId: String
  },
  specifications: [{
    name: String,
    value: String
  }]
}, { timestamps: true });

const categorySchema = new mongoose.Schema({
  name: String,
  description: String,
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const brandSchema = new mongoose.Schema({
  name: String,
  description: String,
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const Package = mongoose.model('Package', packageSchema);
const Category = mongoose.model('Category', categorySchema);
const Brand = mongoose.model('Brand', brandSchema);

// VPS packages data
const vpsPackagesData = [
  {
    name: 'VPS 10 NVMe',
    description: 'Entry-level VPS with NVMe storage - Perfect for small websites and development',
    price: 150,
    vpsConfig: { provider: 'contabo', providerProductId: 'V91' },
    specifications: [
      { name: 'CPU', value: '1 vCPU' },
      { name: 'RAM', value: '4 GB RAM' },
      { name: 'Storage', value: '75 GB NVMe SSD' },
      { name: 'Bandwidth', value: '32 TB Traffic' }
    ]
  },
  {
    name: 'VPS 10 SSD',
    description: 'Entry-level VPS with SSD storage - Reliable performance for small projects',
    price: 150,
    vpsConfig: { provider: 'contabo', providerProductId: 'V92' },
    specifications: [
      { name: 'CPU', value: '1 vCPU' },
      { name: 'RAM', value: '4 GB RAM' },
      { name: 'Storage', value: '100 GB SSD' },
      { name: 'Bandwidth', value: '32 TB Traffic' }
    ]
  },
  {
    name: 'VPS 20 NVMe',
    description: 'Mid-range VPS with NVMe storage - Great for growing applications',
    price: 270,
    vpsConfig: { provider: 'contabo', providerProductId: 'V94' },
    specifications: [
      { name: 'CPU', value: '2 vCPU' },
      { name: 'RAM', value: '8 GB RAM' },
      { name: 'Storage', value: '150 GB NVMe SSD' },
      { name: 'Bandwidth', value: '32 TB Traffic' }
    ]
  },
  {
    name: 'VPS 20 SSD',
    description: 'Mid-range VPS with SSD storage - Balanced performance and storage',
    price: 270,
    vpsConfig: { provider: 'contabo', providerProductId: 'V95' },
    specifications: [
      { name: 'CPU', value: '2 vCPU' },
      { name: 'RAM', value: '8 GB RAM' },
      { name: 'Storage', value: '200 GB SSD' },
      { name: 'Bandwidth', value: '32 TB Traffic' }
    ]
  },
  {
    name: 'VPS 30 NVMe',
    description: 'High-performance VPS with NVMe storage - For demanding applications',
    price: 450,
    vpsConfig: { provider: 'contabo', providerProductId: 'V97' },
    specifications: [
      { name: 'CPU', value: '4 vCPU' },
      { name: 'RAM', value: '16 GB RAM' },
      { name: 'Storage', value: '300 GB NVMe SSD' },
      { name: 'Bandwidth', value: '32 TB Traffic' }
    ]
  },
  {
    name: 'VPS 30 SSD',
    description: 'High-performance VPS with SSD storage - Enterprise-grade reliability',
    price: 450,
    vpsConfig: { provider: 'contabo', providerProductId: 'V98' },
    specifications: [
      { name: 'CPU', value: '4 vCPU' },
      { name: 'RAM', value: '16 GB RAM' },
      { name: 'Storage', value: '400 GB SSD' },
      { name: 'Bandwidth', value: '32 TB Traffic' }
    ]
  }
];

async function createVPSPackages() {
  try {
    console.log('🚀 Starting VPS packages creation...\n');

    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://jakiezian:<EMAIL>/ztech?retryWrites=true&w=majority&appName=ztech-dev';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB\n');

    // Create or get VPS category
    let vpsCategory = await Category.findOne({ name: 'VPS' });
    if (!vpsCategory) {
      vpsCategory = await Category.create({
        name: 'VPS',
        description: 'Virtual Private Server hosting solutions',
        isActive: true
      });
      console.log('✅ Created VPS category');
    } else {
      console.log('✅ VPS category already exists');
    }

    // Create or get Contabo brand
    let contaboBrand = await Brand.findOne({ name: 'Contabo' });
    if (!contaboBrand) {
      contaboBrand = await Brand.create({
        name: 'Contabo',
        description: 'High-performance VPS hosting provider',
        isActive: true
      });
      console.log('✅ Created Contabo brand');
    } else {
      console.log('✅ Contabo brand already exists');
    }

    console.log('\n📦 Creating VPS packages...');

    let createdCount = 0;
    let skippedCount = 0;

    for (const packageData of vpsPackagesData) {
      // Check if package already exists
      const existingPackage = await Package.findOne({
        name: packageData.name,
        'vpsConfig.providerProductId': packageData.vpsConfig.providerProductId
      });

      if (existingPackage) {
        console.log(`⚠️  Package "${packageData.name}" already exists, skipping...`);
        skippedCount++;
        continue;
      }

      // Create the package
      await Package.create({
        ...packageData,
        category: vpsCategory._id,
        brand: contaboBrand._id
      });

      console.log(`✅ Created VPS package: ${packageData.name} (${packageData.vpsConfig.providerProductId})`);
      createdCount++;
    }

    // Verify packages were created
    const vpsPackages = await Package.find({ 'vpsConfig.provider': { $exists: true } });

    console.log(`\n📊 Summary:`);
    console.log(`   ✅ Created: ${createdCount} packages`);
    console.log(`   ⚠️  Skipped: ${skippedCount} packages (already exist)`);
    console.log(`   📦 Total VPS packages in database: ${vpsPackages.length}`);

    console.log(`\n📋 VPS Packages:`);
    vpsPackages.forEach((pkg, index) => {
      console.log(`   ${index + 1}. ${pkg.name} - ${pkg.price} MAD (${pkg.vpsConfig.providerProductId})`);
    });

    console.log('\n🎉 VPS packages setup completed successfully!');
    console.log('🎯 You can now test VPS creation through the frontend or API.');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    
    if (error.message.includes('ENOTFOUND')) {
      console.log('\n💡 Network connectivity issue. Try:');
      console.log('   1. Check your internet connection');
      console.log('   2. Use the API-based script instead: node setup-vps-packages-api.js');
      console.log('   3. Make sure MongoDB Atlas is accessible');
    }
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed.');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  createVPSPackages();
}

module.exports = { createVPSPackages };
