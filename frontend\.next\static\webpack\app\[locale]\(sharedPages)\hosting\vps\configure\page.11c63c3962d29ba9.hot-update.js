"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/vps/configure/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx":
/*!***********************************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfigureVPSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/vpsService */ \"(app-pages-browser)/./src/app/services/vpsService.js\");\n/* harmony import */ var _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/cartService */ \"(app-pages-browser)/./src/app/services/cartService.js\");\n/* harmony import */ var _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/packageService */ \"(app-pages-browser)/./src/app/services/packageService.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/context/AuthContext */ \"(app-pages-browser)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Modern OS Icons Components\nconst UbuntuIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 31,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined);\n};\n_c = UbuntuIcon;\nconst CentOSIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 39,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = CentOSIcon;\nconst DebianIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 47,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = DebianIcon;\nconst WindowsIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 55,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = WindowsIcon;\nfunction ConfigureVPSPage() {\n    var _operatingSystems_find, _locations_find;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"vps_configure\");\n    const { setCartCount } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // State management\n    const [vpsPlans, setVpsPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [osImages, setOsImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [regions, setRegions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderLoading, setOrderLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize plan from URL params\n    const planId = searchParams.get(\"plan\");\n    const autoBackup = searchParams.get(\"autobackup\") === \"true\";\n    const [selectedPlan, setSelectedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAutoBackup, setIsAutoBackup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    // Function to parse specifications from database\n    const parseSpecifications = (specifications, description)=>{\n        let cores = 0, ram = \"0 GB\", storage = \"0 GB\", traffic = \"32 TB\";\n        // Parse from specifications array\n        if (specifications && Array.isArray(specifications)) {\n            specifications.forEach((spec)=>{\n                const value = spec.value || \"\";\n                const lowerValue = value.toLowerCase();\n                // Parse CPU cores\n                if (lowerValue.includes(\"cpu\") || lowerValue.includes(\"core\") || lowerValue.includes(\"vcpu\")) {\n                    const cpuMatch = value.match(/(\\d+)/);\n                    if (cpuMatch) cores = parseInt(cpuMatch[1]);\n                }\n                // Parse RAM\n                if (lowerValue.includes(\"ram\") || lowerValue.includes(\"memory\") || lowerValue.includes(\"gb ram\")) {\n                    const ramMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n                }\n                // Parse Storage\n                if (lowerValue.includes(\"storage\") || lowerValue.includes(\"disk\") || lowerValue.includes(\"ssd\") || lowerValue.includes(\"nvme\")) {\n                    const storageMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (storageMatch) {\n                        const storageType = lowerValue.includes(\"nvme\") ? \"NVMe\" : lowerValue.includes(\"ssd\") ? \"SSD\" : \"\";\n                        storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n                    }\n                }\n                // Parse Traffic/Bandwidth\n                if (lowerValue.includes(\"traffic\") || lowerValue.includes(\"bandwidth\") || lowerValue.includes(\"transfer\")) {\n                    const trafficMatch = value.match(/(\\d+)\\s*(tb|gb)/i);\n                    if (trafficMatch) {\n                        traffic = \"\".concat(trafficMatch[1], \" \").concat(trafficMatch[2].toUpperCase());\n                    }\n                }\n            });\n        }\n        // Fallback: parse from description if specifications are empty\n        if (cores === 0 && description) {\n            const descLower = description.toLowerCase();\n            const cpuMatch = description.match(/(\\d+)\\s*(cpu|core|vcpu)/i);\n            if (cpuMatch) cores = parseInt(cpuMatch[1]);\n            const ramMatch = description.match(/(\\d+)\\s*gb\\s*ram/i);\n            if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n            const storageMatch = description.match(/(\\d+)\\s*gb\\s*(storage|disk|ssd|nvme)/i);\n            if (storageMatch) {\n                const storageType = descLower.includes(\"nvme\") ? \"NVMe\" : descLower.includes(\"ssd\") ? \"SSD\" : \"\";\n                storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n            }\n        }\n        return {\n            cores,\n            ram,\n            storage,\n            traffic\n        };\n    };\n    // Fetch VPS packages and find the selected one\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchVPSPackages = async ()=>{\n            try {\n                setLoading(true);\n                // Récupérer les packages VPS depuis la base de données\n                const response = await _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPackages(\"VPS Hosting\");\n                console.log(\"VPS packages response:\", response);\n                let vpsPackages = [];\n                if (response.data && Array.isArray(response.data)) {\n                    vpsPackages = response.data;\n                } else if (response.data && response.data.packages && Array.isArray(response.data.packages)) {\n                    vpsPackages = response.data.packages;\n                } else if (Array.isArray(response)) {\n                    vpsPackages = response;\n                }\n                // Transformer les packages de la base de données\n                const transformedPlans = vpsPackages.map((pkg)=>{\n                    const specs = parseSpecifications(pkg.specifications, pkg.description);\n                    return {\n                        id: pkg._id,\n                        _id: pkg._id,\n                        name: pkg.name,\n                        price: pkg.price,\n                        cores: specs.cores,\n                        ram: specs.ram,\n                        storage: specs.storage,\n                        traffic: specs.traffic,\n                        description: pkg.description,\n                        specifications: pkg.specifications\n                    };\n                });\n                setVpsPlans(transformedPlans);\n                // Trouver le package sélectionné par son ID\n                if (planId && transformedPlans.length > 0) {\n                    const foundPlan = transformedPlans.find((plan)=>{\n                        var _plan__id, _plan_id;\n                        return plan._id === planId || plan.id === planId || ((_plan__id = plan._id) === null || _plan__id === void 0 ? void 0 : _plan__id.toString()) === planId || ((_plan_id = plan.id) === null || _plan_id === void 0 ? void 0 : _plan_id.toString()) === planId;\n                    });\n                    if (foundPlan) {\n                        setSelectedPlan(foundPlan);\n                        console.log(\"Selected plan found:\", foundPlan);\n                    } else {\n                        console.error(\"Plan not found with ID:\", planId);\n                        console.log(\"Available plans:\", transformedPlans.map((p)=>({\n                                id: p.id,\n                                _id: p._id,\n                                name: p.name\n                            })));\n                        setError(\"Package VPS non trouv\\xe9\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error fetching VPS plans:\", error);\n                setError(\"Erreur lors du chargement des plans VPS\");\n                setVpsPlans([]);\n                setSelectedPlan(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        // Fetch dynamic OS images from API\n        const fetchOSImages = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching OS images from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                console.log(\"✅ OS Images response:\", response);\n                let images = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    images = response.data.data;\n                } else if (response.data && response.data.images && Array.isArray(response.data.images)) {\n                    images = response.data.images;\n                }\n                // Transform API data to expected format\n                const transformedImages = images.map((img)=>({\n                        id: img.imageId || img.id,\n                        name: img.name,\n                        description: img.description,\n                        type: img.osType || \"linux\",\n                        version: img.version,\n                        provider: img.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting OS images:\", transformedImages.length, \"images\");\n                setOsImages(transformedImages);\n                // Set default OS if available\n                if (transformedImages.length > 0) {\n                    const defaultOS = transformedImages.find((img)=>img.name.toLowerCase().includes(\"ubuntu\") && img.name.toLowerCase().includes(\"22.04\")) || transformedImages[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default OS:\", defaultOS.name, defaultOS.id);\n                    setSelectedOS(defaultOS.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching OS images:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticOsImages = [\n                    {\n                        id: \"ubuntu-20.04\",\n                        name: \"Ubuntu 20.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"ubuntu-22.04\",\n                        name: \"Ubuntu 22.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"centos-7\",\n                        name: \"CentOS 7\",\n                        type: \"centos\"\n                    },\n                    {\n                        id: \"debian-11\",\n                        name: \"Debian 11\",\n                        type: \"debian\"\n                    }\n                ];\n                setOsImages(staticOsImages);\n                setSelectedOS(\"ubuntu-22.04\");\n            }\n        };\n        // Fetch dynamic regions from API\n        const fetchRegions = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching regions from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                console.log(\"✅ Regions response:\", response);\n                let regions = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    regions = response.data.data;\n                } else if (response.data && response.data.regions && Array.isArray(response.data.regions)) {\n                    regions = response.data.regions;\n                }\n                // Transform API data to expected format\n                const transformedRegions = regions.map((region)=>({\n                        id: region.regionSlug,\n                        name: region.regionName,\n                        provider: region.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting regions:\", transformedRegions.length, \"regions\");\n                setRegions(transformedRegions);\n                // Set default region if available\n                if (transformedRegions.length > 0) {\n                    const defaultRegion = transformedRegions.find((region)=>region.id === \"EU\") || transformedRegions[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default region:\", defaultRegion.name, defaultRegion.id);\n                    setSelectedLocation(defaultRegion.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching regions:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticRegions = [\n                    {\n                        id: \"EU\",\n                        name: \"European Union\",\n                        description: \"Germany\",\n                        country: \"Germany\",\n                        city: \"Nuremberg\"\n                    },\n                    {\n                        id: \"US-central\",\n                        name: \"United States Central\",\n                        description: \"St. Louis\",\n                        country: \"United States\",\n                        city: \"St. Louis\"\n                    },\n                    {\n                        id: \"SG\",\n                        name: \"Asia Pacific\",\n                        description: \"Singapore\",\n                        country: \"Singapore\",\n                        city: \"Singapore\"\n                    }\n                ];\n                setRegions(staticRegions);\n                setSelectedLocation(\"EU\");\n            }\n        };\n        // Appeler les fonctions pour récupérer les données\n        fetchVPSPackages();\n        fetchOSImages();\n        fetchRegions();\n    }, [\n        planId\n    ]);\n    // Configuration state\n    const [selectedOS, setSelectedOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ubuntu-20.04\");\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"france\");\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [additionalIPs, setAdditionalIPs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [backupEnabled, setBackupEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // New Contabo-style options\n    const [autoBackupOption, setAutoBackupOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [privateNetworking, setPrivateNetworking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ipv4Addresses, setIpv4Addresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [objectStorage, setObjectStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [serverManagement, setServerManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"unmanaged\");\n    const [monitoring, setMonitoring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ssl, setSsl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    // Login credentials state\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"root\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Password validation function\n    const validatePassword = (pwd)=>{\n        if (!pwd) {\n            return \"Please enter a valid password\";\n        }\n        if (pwd.length < 8) {\n            return \"Password must be at least 8 characters long\";\n        }\n        if (!/(?=.*[a-z])/.test(pwd)) {\n            return \"Password must contain at least one lowercase letter\";\n        }\n        if (!/(?=.*[A-Z])/.test(pwd)) {\n            return \"Password must contain at least one uppercase letter\";\n        }\n        if (!/(?=.*\\d)/.test(pwd)) {\n            return \"Password must contain at least one number\";\n        }\n        if (!/(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?])/.test(pwd)) {\n            return \"Password must contain at least one special character\";\n        }\n        return \"\";\n    };\n    // Generate secure password function\n    const generateSecurePassword = ()=>{\n        const lowercase = \"abcdefghijklmnopqrstuvwxyz\";\n        const uppercase = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n        const numbers = \"0123456789\";\n        const symbols = \"!@#$%^&*()_+-=[]{}|;:,.<>?\";\n        const allChars = lowercase + uppercase + numbers + symbols;\n        let newPassword = \"\";\n        // Ensure at least one character from each category\n        newPassword += lowercase[Math.floor(Math.random() * lowercase.length)];\n        newPassword += uppercase[Math.floor(Math.random() * uppercase.length)];\n        newPassword += numbers[Math.floor(Math.random() * numbers.length)];\n        newPassword += symbols[Math.floor(Math.random() * symbols.length)];\n        // Fill the rest randomly\n        for(let i = 4; i < 16; i++){\n            newPassword += allChars[Math.floor(Math.random() * allChars.length)];\n        }\n        // Shuffle the password\n        newPassword = newPassword.split(\"\").sort(()=>Math.random() - 0.5).join(\"\");\n        setPassword(newPassword);\n        setPasswordError(\"\");\n    };\n    // Handle password change\n    const handlePasswordChange = (e)=>{\n        const newPassword = e.target.value;\n        setPassword(newPassword);\n        setPasswordError(validatePassword(newPassword));\n    };\n    // Generate initial password on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!password) {\n            generateSecurePassword();\n        }\n    }, []);\n    // Handle adding VPS to cart\n    const handleAddToCart = async ()=>{\n        if (!selectedPlan) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez s\\xe9lectionner un plan VPS\");\n            return;\n        }\n        // Validate password\n        const passwordValidationError = validatePassword(password);\n        if (passwordValidationError) {\n            setPasswordError(passwordValidationError);\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please enter a valid password\");\n            return;\n        }\n        try {\n            var _selectedPlan_vpsConfig, _response_data_cart, _response_data;\n            setOrderLoading(true);\n            // Vérifier si nous avons un ID valide\n            const packageId = selectedPlan._id || selectedPlan.id;\n            if (!packageId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"ID du package VPS manquant. Veuillez r\\xe9essayer.\");\n                return;\n            }\n            // Map frontend selections to Contabo API format\n            const contaboRegionMap = {\n                \"france\": \"EU\",\n                \"EU\": \"EU\",\n                \"germany\": \"EU\",\n                \"US-central\": \"US-east\",\n                \"usa\": \"US-east\",\n                \"SG\": \"SIN\",\n                \"singapore\": \"SIN\",\n                \"asia\": \"SIN\"\n            };\n            const contaboOSMap = {\n                \"ubuntu-20.04\": \"ubuntu-20.04\",\n                \"ubuntu-22.04\": \"ubuntu-22.04\",\n                \"ubuntu-24.04\": \"ubuntu-24.04\",\n                \"centos-7\": \"centos-7\",\n                \"centos-8\": \"centos-8\",\n                \"debian-10\": \"debian-10\",\n                \"debian-11\": \"debian-11\",\n                \"windows-2019\": \"windows-server-2019\",\n                \"windows-2022\": \"windows-server-2022\"\n            };\n            // Generate display name if not provided\n            const displayName = \"\".concat(selectedPlan.name, \"-\").concat(Date.now());\n            // Préparer les données pour l'ajout au panier avec configuration Contabo\n            const cartData = {\n                packageId: packageId,\n                quantity: quantity,\n                period: selectedPeriod === \"monthly\" ? 1 : selectedPeriod === \"6months\" ? 6 : 12,\n                // Configuration personnalisée pour Contabo VPS\n                customConfiguration: {\n                    // Contabo API fields\n                    planId: ((_selectedPlan_vpsConfig = selectedPlan.vpsConfig) === null || _selectedPlan_vpsConfig === void 0 ? void 0 : _selectedPlan_vpsConfig.providerProductId) || selectedPlan.id,\n                    provider: \"contabo\",\n                    region: contaboRegionMap[selectedLocation] || \"EU\",\n                    operatingSystem: contaboOSMap[selectedOS] || selectedOS,\n                    displayName: displayName,\n                    username: username,\n                    rootPassword: password,\n                    sshKeys: [],\n                    userData: \"\",\n                    addons: {\n                        privatenetworking: privateNetworking !== \"none\",\n                        autobackup: autoBackupOption !== \"none\",\n                        monitoring: monitoring !== \"none\"\n                    },\n                    // Plan specifications for reference\n                    cpu: selectedPlan.cores || selectedPlan.cpu,\n                    ram: selectedPlan.ram,\n                    storage: selectedPlan.storage,\n                    bandwidth: selectedPlan.traffic || selectedPlan.bandwidth,\n                    // Frontend-specific fields for display\n                    frontendConfig: {\n                        operatingSystem: selectedOS,\n                        location: selectedLocation,\n                        additionalIPs: additionalIPs,\n                        backup: isAutoBackup || backupEnabled,\n                        planName: selectedPlan.name,\n                        autoBackupOption: autoBackupOption,\n                        privateNetworking: privateNetworking,\n                        ipv4Addresses: ipv4Addresses,\n                        objectStorage: objectStorage,\n                        serverManagement: serverManagement,\n                        monitoring: monitoring,\n                        ssl: ssl\n                    }\n                }\n            };\n            console.log(\"Adding VPS to cart:\", cartData);\n            console.log(\"Selected plan:\", selectedPlan);\n            // Ajouter au panier via le service\n            const response = await _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addItemToCart(cartData);\n            console.log(\"Cart response:\", response);\n            // Mettre à jour le compteur du panier\n            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_cart = _response_data.cart) === null || _response_data_cart === void 0 ? void 0 : _response_data_cart.cartCount) {\n                setCartCount(response.data.cart.cartCount);\n            }\n            // Afficher le message de succès\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(selectedPlan.name, \" ajout\\xe9 au panier avec succ\\xe8s!\"));\n            // Rediriger vers le panier\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response2, _error_response3, _error_response4;\n            console.error(\"Error adding VPS to cart:\", error);\n            console.error(\"Error response:\", error.response);\n            console.error(\"Error data:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // Gestion des erreurs spécifiques\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.response.data.message);\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Package VPS non trouv\\xe9. Veuillez contacter le support.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez vous connecter pour ajouter au panier.\");\n                router.push(\"/auth/login\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Donn\\xe9es invalides. Veuillez v\\xe9rifier votre s\\xe9lection.\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Erreur lors de l'ajout au panier. Veuillez r\\xe9essayer.\");\n            }\n        } finally{\n            setOrderLoading(false);\n        }\n    };\n    // Use dynamic OS images data with fallback to static data\n    const operatingSystems = osImages.length > 0 ? osImages.map((os)=>({\n            id: os.id,\n            name: os.name,\n            icon: getOSIcon(os.type || os.osType),\n            type: os.type || os.osType || \"linux\",\n            description: os.description,\n            version: os.version,\n            provider: os.provider\n        })) : [\n        {\n            id: \"ubuntu-20.04\",\n            name: \"Ubuntu 20.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"ubuntu-22.04\",\n            name: \"Ubuntu 22.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"centos-8\",\n            name: \"CentOS 8\",\n            icon: CentOSIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"debian-11\",\n            name: \"Debian 11\",\n            icon: DebianIcon,\n            type: \"linux\"\n        }\n    ];\n    // Helper function to get OS icon based on type\n    function getOSIcon(osType) {\n        const iconMap = {\n            \"ubuntu\": UbuntuIcon,\n            \"centos\": CentOSIcon,\n            \"debian\": DebianIcon,\n            \"windows\": WindowsIcon,\n            \"linux\": UbuntuIcon // default for linux\n        };\n        // Check if osType contains specific OS names\n        if (osType && typeof osType === \"string\") {\n            const lowerType = osType.toLowerCase();\n            if (lowerType.includes(\"ubuntu\")) return UbuntuIcon;\n            if (lowerType.includes(\"centos\")) return CentOSIcon;\n            if (lowerType.includes(\"debian\")) return DebianIcon;\n            if (lowerType.includes(\"windows\")) return WindowsIcon;\n        }\n        return iconMap[osType] || UbuntuIcon;\n    }\n    // Use dynamic regions data with fallback to static data\n    const locations = regions.length > 0 ? regions.map((region)=>({\n            id: region.id,\n            name: region.name,\n            flag: getRegionFlag(region.country),\n            ping: getRegionPing(region.id),\n            description: region.description,\n            city: region.city,\n            country: region.country\n        })) : [\n        {\n            id: \"EU\",\n            name: \"European Union\",\n            flag: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            ping: \"15ms\",\n            description: \"Germany\",\n            city: \"Nuremberg\",\n            country: \"Germany\"\n        },\n        {\n            id: \"US-central\",\n            name: \"United States Central\",\n            flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            ping: \"120ms\",\n            description: \"St. Louis\",\n            city: \"St. Louis\",\n            country: \"United States\"\n        },\n        {\n            id: \"SG\",\n            name: \"Asia Pacific\",\n            flag: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            ping: \"200ms\",\n            description: \"Singapore\",\n            city: \"Singapore\",\n            country: \"Singapore\"\n        }\n    ];\n    // Helper functions for region display\n    function getRegionFlag(country) {\n        const flagMap = {\n            \"Germany\": \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            \"United States\": \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            \"Singapore\": \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            \"France\": \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            \"Netherlands\": \"\\uD83C\\uDDF3\\uD83C\\uDDF1\"\n        };\n        return flagMap[country] || \"\\uD83C\\uDF0D\";\n    }\n    function getRegionPing(regionId) {\n        const pingMap = {\n            \"EU\": \"15ms\",\n            \"US-central\": \"120ms\",\n            \"US-east\": \"110ms\",\n            \"US-west\": \"130ms\",\n            \"SG\": \"200ms\"\n        };\n        return pingMap[regionId] || \"50ms\";\n    }\n    const calculateTotal = ()=>{\n        if (!selectedPlan) return 0;\n        let total = selectedPlan.price;\n        // Additional IPs cost\n        total += additionalIPs * 15; // 15 MAD per additional IP\n        // Backup cost (only if not auto backup plan)\n        if (!isAutoBackup && backupEnabled) {\n            total += 20; // 20 MAD for backup\n        }\n        // Auto Backup cost (Contabo style)\n        if (autoBackupOption === \"auto\") {\n            total += 18; // €1.79 ≈ 18 MAD per month\n        }\n        // Private Networking cost\n        if (privateNetworking === \"enabled\") {\n            total += 28; // 28 MAD per month\n        }\n        // IPv4 additional addresses cost\n        if (ipv4Addresses === 2) {\n            total += 42; // 42 MAD per additional IP\n        }\n        // Object Storage cost\n        const objectStorageCosts = {\n            \"250gb\": 50,\n            \"500gb\": 95,\n            \"750gb\": 140,\n            \"1tb\": 180\n        };\n        if (objectStorage !== \"none\" && objectStorageCosts[objectStorage]) {\n            total += objectStorageCosts[objectStorage];\n        }\n        // Server Management cost\n        if (serverManagement === \"managed\") {\n            total += 1340; // 1340 MAD per month for managed\n        }\n        // Monitoring cost\n        if (monitoring === \"full\") {\n            total += 140; // 140 MAD per month for full monitoring\n        }\n        // SSL cost (one-time charges, but we'll add monthly equivalent)\n        const sslCosts = {\n            \"basic\": 76,\n            \"wildcard\": 228 // 2740 MAD / 12 months ≈ 228 MAD per month\n        };\n        if (ssl !== \"none\" && sslCosts[ssl]) {\n            total += sslCosts[ssl];\n        }\n        // Apply quantity\n        total *= quantity;\n        // Period multiplier\n        const multipliers = {\n            monthly: 1,\n            \"6months\": 6 * 0.97,\n            annually: 12 * 0.90 // 10% discount\n        };\n        return total * multipliers[selectedPeriod];\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 696,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600\",\n                        children: \"Loading VPS configuration...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 697,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 695,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 694,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-red-600 mb-2\",\n                        children: \"Error loading VPS configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 713,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        color: \"blue\",\n                        size: \"sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 716,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 709,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 708,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 sm:gap-4 w-full sm:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outlined\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.history.back(),\n                                        className: \"border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: t(\"back\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h4\",\n                                                className: \"text-lg sm:text-2xl text-gray-900 font-bold truncate\",\n                                                children: t(\"page_title\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 truncate\",\n                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left sm:text-right w-full sm:w-auto flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                        children: t(\"price_from\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        variant: \"h3\",\n                                        className: \"text-lg sm:text-2xl text-blue-600 font-bold\",\n                                        children: [\n                                            (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0,\n                                            \" MAD\",\n                                            t(\"per_month\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 733,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 732,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 731,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6 sm:space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 sm:w-6 h-5 sm:h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                variant: \"h5\",\n                                                                className: \"text-lg sm:text-xl text-gray-900 font-bold truncate\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isAutoBackup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                        children: \"Auto Backup Inclus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"vcpu_cores\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.cores) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"ram\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.ram) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"storage\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.storage) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"traffic\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.traffic) || \"0 TB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"1. \",\n                                                    t(\"billing_period\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"billing_period_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        id: \"monthly\",\n                                                        label: \"1 mois\",\n                                                        discount: \"\",\n                                                        price: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0\n                                                    },\n                                                    {\n                                                        id: \"6months\",\n                                                        label: \"6 mois\",\n                                                        discount: \"3% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 6 * 0.97)\n                                                    },\n                                                    {\n                                                        id: \"annually\",\n                                                        label: \"12 mois\",\n                                                        discount: \"10% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 12 * 0.90)\n                                                    }\n                                                ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedPeriod(period.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 rounded-full border-2 flex-shrink-0 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-600\" : \"border-gray-300\"),\n                                                                            children: selectedPeriod === period.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 843,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 839,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                                    children: period.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                period.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                                    children: period.discount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 849,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                            children: [\n                                                                                period.price,\n                                                                                \" MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 854,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: period.id === \"monthly\" ? \"/mois\" : period.id === \"6months\" ? \"/6 mois\" : \"/an\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 855,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, period.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"2. \",\n                                                    t(\"choose_os\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_os_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 17\n                                            }, this),\n                                            osImages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des syst\\xe8mes d'exploitation...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            operatingSystems.length,\n                                                            \" OS disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching OS images...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                                                                console.log(\"✅ Force fetch result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des OS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        osImages.length,\n                                                        \" syst\\xe8mes d'exploitation charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 895,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: operatingSystems.map((os)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedOS(os.id),\n                                                        className: \"relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedOS === os.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 sm:gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(os.icon, {\n                                                                    className: \"w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-sm sm:text-base text-gray-900 break-words\",\n                                                                            children: os.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 913,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                                                            children: os.type === \"linux\" ? \"Linux Distribution\" : \"Windows Server\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 914,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedOS === os.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, os.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"3. \",\n                                                    t(\"choose_location\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_location_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 17\n                                            }, this),\n                                            regions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des emplacements...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            locations.length,\n                                                            \" emplacements disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching regions...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                                                                console.log(\"✅ Force fetch regions result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch regions error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-green-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des r\\xe9gions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        regions.length,\n                                                        \" emplacements charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 957,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: locations.map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedLocation(location.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedLocation === location.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl sm:text-2xl flex-shrink-0\",\n                                                                            children: location.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 974,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium text-sm sm:text-base text-gray-900 truncate\",\n                                                                                children: location.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 976,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedLocation === location.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 sm:w-5 h-4 sm:h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, location.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"4. Data Protection with Auto Backup\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"auto\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"auto\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"auto\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1007,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full inline-block mb-3\",\n                                                                        children: \"Notre Recommandation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1016,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2\",\n                                                                        children: \"Auto Backup\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1019,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-blue-600 font-bold text-lg mb-3\",\n                                                                        children: \"18 MAD/mois\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-600 mb-2 font-medium\",\n                                                                        children: \"Set it and forget it.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1021,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-500 text-sm mb-4\",\n                                                                        children: \"Data security with no effort\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1022,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1026,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"automated\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1027,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1025,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1030,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"daily\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1031,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1034,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"1-Click Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1035,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1033,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1038,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"10 days\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1039,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1037,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1024,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1015,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"none\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"none\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1058,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1057,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2 mt-8\",\n                                                                        children: \"None\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1065,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-600 font-bold text-lg mb-6\",\n                                                                        children: \"Free\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1070,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1069,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1074,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"on demand\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1075,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1078,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1079,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1077,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1082,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1083,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1081,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 995,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"5. Networking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Private Networking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: privateNetworking,\n                                                                        onChange: (e)=>setPrivateNetworking(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"No Private Networking\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1109,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"enabled\",\n                                                                                children: \"Private Networking Enabled\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1110,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: privateNetworking === \"enabled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"28 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1114,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1116,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1112,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1103,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Bandwidth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                        children: \"32 TB Out + Unlimited In\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1126,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs sm:text-sm text-purple-600\",\n                                                                        children: \"200 Mbit/s Connection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1127,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1125,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"IPv4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1133,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ipv4Addresses,\n                                                                        onChange: (e)=>setIpv4Addresses(parseInt(e.target.value)),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1,\n                                                                                children: \"1 IP Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1140,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 2,\n                                                                                children: \"1 IP Address + 1 Additional IP\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1141,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1135,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: ipv4Addresses === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"42 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1145,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1147,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1143,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1132,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"6. Add-Ons\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Object Storage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: objectStorage,\n                                                                        onChange: (e)=>setObjectStorage(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1173,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"250gb\",\n                                                                                children: \"250 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1174,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"500gb\",\n                                                                                children: \"500 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1175,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"750gb\",\n                                                                                children: \"750 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1176,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"1tb\",\n                                                                                children: \"1 TB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1177,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1168,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            objectStorage === \"250gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"50 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1180,\n                                                                                columnNumber: 55\n                                                                            }, this),\n                                                                            objectStorage === \"500gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"95 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1181,\n                                                                                columnNumber: 55\n                                                                            }, this),\n                                                                            objectStorage === \"750gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"140 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1182,\n                                                                                columnNumber: 55\n                                                                            }, this),\n                                                                            objectStorage === \"1tb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"180 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1183,\n                                                                                columnNumber: 53\n                                                                            }, this),\n                                                                            objectStorage === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1184,\n                                                                                columnNumber: 54\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1179,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1167,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Server Management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1191,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: serverManagement,\n                                                                        onChange: (e)=>setServerManagement(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"unmanaged\",\n                                                                                children: \"Unmanaged\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1198,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"managed\",\n                                                                                children: \"Managed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1199,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1193,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: serverManagement === \"unmanaged\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1203,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"1340 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1205,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1201,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1192,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Monitoring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1213,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: monitoring,\n                                                                        onChange: (e)=>setMonitoring(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1220,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"full\",\n                                                                                children: \"Full Monitoring\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1221,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1215,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: monitoring === \"full\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"140 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1227,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1223,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1214,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"SSL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1235,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ssl,\n                                                                        onChange: (e)=>setSsl(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1242,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"basic\",\n                                                                                children: \"SSL certificate\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1243,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"wildcard\",\n                                                                                children: \"SSL certificate (wildcard)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1244,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1237,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            ssl === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"914 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1249,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1250,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1248,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"wildcard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"2740 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1255,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1256,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1254,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1259,\n                                                                                columnNumber: 44\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1246,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1236,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-700 font-semibold mb-6\",\n                                                children: \"8. Login & password for your server\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900 min-w-[100px]\",\n                                                                children: \"Username\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1277,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700 bg-gray-50 px-3 py-2 rounded border\",\n                                                                children: \"root\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col sm:flex-row sm:items-start gap-2 sm:gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"font-medium text-sm sm:text-base text-gray-900 min-w-[100px] sm:mt-2\",\n                                                                        children: \"Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1288,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{},\n                                                                                className: \"text-blue-600 hover:text-blue-800 text-sm underline mb-2\",\n                                                                                children: \"Generate new password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1292,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: showPassword ? \"text\" : \"password\",\n                                                                                        value: password,\n                                                                                        onChange: handlePasswordChange,\n                                                                                        placeholder: \"Enter a secure password\",\n                                                                                        className: \"w-full border rounded px-3 py-2 pr-12 text-sm sm:text-base \".concat(passwordError ? \"border-red-500 bg-red-50\" : \"border-gray-300 bg-white\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1300,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                            lineNumber: 1315,\n                                                                                            columnNumber: 31\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                            className: \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                            lineNumber: 1317,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1309,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1299,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1291,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1287,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-red-600 text-sm ml-0 sm:ml-[116px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-4 h-4 rounded-full bg-red-600 text-white flex items-center justify-center text-xs font-bold\",\n                                                                        children: \"!\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1326,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Please enter a valid password\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1325,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-600 text-sm ml-0 sm:ml-[116px] mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"In order to use SSH Keys you can add them in the Customer Control Panel later. Your password will not be sent via email. Be sure to remember it for Windows access. If you forget the password, you will need to reinstall your server.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1331,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1268,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 766,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:sticky lg:top-4 lg:max-h-[calc(100vh-2rem)] lg:overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg h-full flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6 flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6 flex-shrink-0\",\n                                                children: t(\"order_summary\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 sm:space-y-4 mb-4 sm:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base text-gray-600\",\n                                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1359,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: \"Quantit\\xe9:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1361,\n                                                                                        columnNumber: 25\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                                                disabled: quantity === 1,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"-\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1363,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"w-8 text-center font-medium text-xs\",\n                                                                                                children: quantity\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1372,\n                                                                                                columnNumber: 27\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.min(10, quantity + 1)),\n                                                                                                disabled: quantity === 10,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"+\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1373,\n                                                                                                columnNumber: 27\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1362,\n                                                                                        columnNumber: 25\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1360,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1358,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            ((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1357,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            additionalIPs > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"IPs additionnelles (\",\n                                                                            additionalIPs,\n                                                                            \" \\xd7 \",\n                                                                            quantity,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1390,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            additionalIPs * 15 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1389,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            !isAutoBackup && backupEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"Sauvegarde automatique \\xd7 \",\n                                                                            quantity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            20 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            selectedPeriod !== \"monthly\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"R\\xe9duction (\",\n                                                                            selectedPeriod === \"6months\" ? \"3%\" : \"10%\",\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1404,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            \"-\",\n                                                                            Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity * (selectedPeriod === \"6months\" ? 6 * 0.03 : 12 * 0.10)),\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1403,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                className: \"border-gray-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1409,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-base sm:text-lg font-bold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: t(\"total\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 flex-shrink-0 text-right\",\n                                                                        children: [\n                                                                            Math.round(calculateTotal()),\n                                                                            \" MAD/\",\n                                                                            selectedPeriod === \"monthly\" ? \"mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"an\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1413,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1411,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs sm:text-sm text-gray-600 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"os_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1419,\n                                                                                    columnNumber: 55\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_operatingSystems_find = operatingSystems.find((os)=>os.id === selectedOS)) === null || _operatingSystems_find === void 0 ? void 0 : _operatingSystems_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1419,\n                                                                                    columnNumber: 94\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1419,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"location_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1420,\n                                                                                    columnNumber: 55\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_locations_find = locations.find((loc)=>loc.id === selectedLocation)) === null || _locations_find === void 0 ? void 0 : _locations_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1420,\n                                                                                    columnNumber: 100\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1420,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"P\\xe9riode:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1421,\n                                                                                    columnNumber: 55\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: selectedPeriod === \"monthly\" ? \"1 mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"12 mois\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1421,\n                                                                                    columnNumber: 86\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1421,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1418,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1417,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1356,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"lg\",\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold\",\n                                                        onClick: handleAddToCart,\n                                                        disabled: orderLoading || loading || !selectedPlan,\n                                                        children: orderLoading ? \"Ajout en cours...\" : \"Ajouter au panier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1437,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Paiement s\\xe9curis\\xe9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1438,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1436,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1441,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"D\\xe9ploiement en 5 minutes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1442,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1440,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1435,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1349,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1348,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 1347,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 1346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 764,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 763,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 729,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigureVPSPage, \"O9j3Z4JEEGcRkcr57PypU72+QVM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations,\n        _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c4 = ConfigureVPSPage;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"UbuntuIcon\");\n$RefreshReg$(_c1, \"CentOSIcon\");\n$RefreshReg$(_c2, \"DebianIcon\");\n$RefreshReg$(_c3, \"WindowsIcon\");\n$RefreshReg$(_c4, \"ConfigureVPSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vKHNoYXJlZFBhZ2VzKS9ob3N0aW5nL3Zwcy9jb25maWd1cmUvcGFnZS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNEM7QUFDaUI7QUFDakI7QUFDa0M7QUFDM0I7QUFDRTtBQUNNO0FBQ1A7QUFDYjtBQWdCakI7QUFFdEIsNkJBQTZCO0FBQzdCLE1BQU00QixhQUFhO1FBQUMsRUFBRUMsU0FBUyxFQUFFO3lCQUMvQiw4REFBQ0M7UUFBSUQsV0FBVyxHQUFhLE9BQVZBLFdBQVU7a0JBQzNCLDRFQUFDRTtZQUFJQyxTQUFRO1lBQVlILFdBQVU7WUFBeUJJLE1BQUs7c0JBQy9ELDRFQUFDQztnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7S0FIUlA7QUFRTixNQUFNUSxhQUFhO1FBQUMsRUFBRVAsU0FBUyxFQUFFO3lCQUMvQiw4REFBQ0M7UUFBSUQsV0FBVyxHQUFhLE9BQVZBLFdBQVU7a0JBQzNCLDRFQUFDRTtZQUFJQyxTQUFRO1lBQVlILFdBQVU7WUFBeUJJLE1BQUs7c0JBQy9ELDRFQUFDQztnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7TUFIUkM7QUFRTixNQUFNQyxhQUFhO1FBQUMsRUFBRVIsU0FBUyxFQUFFO3lCQUMvQiw4REFBQ0M7UUFBSUQsV0FBVyxHQUFhLE9BQVZBLFdBQVU7a0JBQzNCLDRFQUFDRTtZQUFJQyxTQUFRO1lBQVlILFdBQVU7WUFBeUJJLE1BQUs7c0JBQy9ELDRFQUFDQztnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7TUFIUkU7QUFRTixNQUFNQyxjQUFjO1FBQUMsRUFBRVQsU0FBUyxFQUFFO3lCQUNoQyw4REFBQ0M7UUFBSUQsV0FBVyxHQUFhLE9BQVZBLFdBQVU7a0JBQzNCLDRFQUFDRTtZQUFJQyxTQUFRO1lBQVlILFdBQVU7WUFBeUJJLE1BQUs7c0JBQy9ELDRFQUFDQztnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7TUFIUkc7QUFRUyxTQUFTQztRQTgwQ2tHQyx3QkFDTUM7O0lBOTBDOUgsTUFBTUMsZUFBZXhDLGdFQUFlQTtJQUNwQyxNQUFNeUMsU0FBU3hDLDBEQUFTQTtJQUN4QixNQUFNeUMsSUFBSXhDLDBEQUFlQSxDQUFDO0lBQzFCLE1BQU0sRUFBRXlDLFlBQVksRUFBRSxHQUFHakMsaUVBQU9BO0lBRWhDLG1CQUFtQjtJQUNuQixNQUFNLENBQUNrQyxVQUFVQyxZQUFZLEdBQUcvQywrQ0FBUUEsQ0FBQyxFQUFFO0lBQzNDLE1BQU0sQ0FBQ2dELFVBQVVDLFlBQVksR0FBR2pELCtDQUFRQSxDQUFDLEVBQUU7SUFDM0MsTUFBTSxDQUFDa0QsU0FBU0MsV0FBVyxHQUFHbkQsK0NBQVFBLENBQUMsRUFBRTtJQUN6QyxNQUFNLENBQUNvRCxTQUFTQyxXQUFXLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzRCxPQUFPQyxTQUFTLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUN3RCxjQUFjQyxnQkFBZ0IsR0FBR3pELCtDQUFRQSxDQUFDO0lBRWpELGtDQUFrQztJQUNsQyxNQUFNMEQsU0FBU2hCLGFBQWFpQixHQUFHLENBQUM7SUFDaEMsTUFBTUMsYUFBYWxCLGFBQWFpQixHQUFHLENBQUMsa0JBQWtCO0lBRXRELE1BQU0sQ0FBQ0UsY0FBY0MsZ0JBQWdCLEdBQUc5RCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMrRCxjQUFjQyxnQkFBZ0IsR0FBR2hFLCtDQUFRQSxDQUFDNEQ7SUFFakQsaURBQWlEO0lBQ2pELE1BQU1LLHNCQUFzQixDQUFDQyxnQkFBZ0JDO1FBQzNDLElBQUlDLFFBQVEsR0FBR0MsTUFBTSxRQUFRQyxVQUFVLFFBQVFDLFVBQVU7UUFFekQsa0NBQWtDO1FBQ2xDLElBQUlMLGtCQUFrQk0sTUFBTUMsT0FBTyxDQUFDUCxpQkFBaUI7WUFDbkRBLGVBQWVRLE9BQU8sQ0FBQ0MsQ0FBQUE7Z0JBQ3JCLE1BQU1DLFFBQVFELEtBQUtDLEtBQUssSUFBSTtnQkFDNUIsTUFBTUMsYUFBYUQsTUFBTUUsV0FBVztnQkFFcEMsa0JBQWtCO2dCQUNsQixJQUFJRCxXQUFXRSxRQUFRLENBQUMsVUFBVUYsV0FBV0UsUUFBUSxDQUFDLFdBQVdGLFdBQVdFLFFBQVEsQ0FBQyxTQUFTO29CQUM1RixNQUFNQyxXQUFXSixNQUFNSyxLQUFLLENBQUM7b0JBQzdCLElBQUlELFVBQVVaLFFBQVFjLFNBQVNGLFFBQVEsQ0FBQyxFQUFFO2dCQUM1QztnQkFFQSxZQUFZO2dCQUNaLElBQUlILFdBQVdFLFFBQVEsQ0FBQyxVQUFVRixXQUFXRSxRQUFRLENBQUMsYUFBYUYsV0FBV0UsUUFBUSxDQUFDLFdBQVc7b0JBQ2hHLE1BQU1JLFdBQVdQLE1BQU1LLEtBQUssQ0FBQztvQkFDN0IsSUFBSUUsVUFBVWQsTUFBTSxHQUFlLE9BQVpjLFFBQVEsQ0FBQyxFQUFFLEVBQUM7Z0JBQ3JDO2dCQUVBLGdCQUFnQjtnQkFDaEIsSUFBSU4sV0FBV0UsUUFBUSxDQUFDLGNBQWNGLFdBQVdFLFFBQVEsQ0FBQyxXQUFXRixXQUFXRSxRQUFRLENBQUMsVUFBVUYsV0FBV0UsUUFBUSxDQUFDLFNBQVM7b0JBQzlILE1BQU1LLGVBQWVSLE1BQU1LLEtBQUssQ0FBQztvQkFDakMsSUFBSUcsY0FBYzt3QkFDaEIsTUFBTUMsY0FBY1IsV0FBV0UsUUFBUSxDQUFDLFVBQVUsU0FBU0YsV0FBV0UsUUFBUSxDQUFDLFNBQVMsUUFBUTt3QkFDaEdULFVBQVUsR0FBeUJlLE9BQXRCRCxZQUFZLENBQUMsRUFBRSxFQUFDLFFBQWtCLE9BQVpDLGFBQWNDLElBQUk7b0JBQ3ZEO2dCQUNGO2dCQUVBLDBCQUEwQjtnQkFDMUIsSUFBSVQsV0FBV0UsUUFBUSxDQUFDLGNBQWNGLFdBQVdFLFFBQVEsQ0FBQyxnQkFBZ0JGLFdBQVdFLFFBQVEsQ0FBQyxhQUFhO29CQUN6RyxNQUFNUSxlQUFlWCxNQUFNSyxLQUFLLENBQUM7b0JBQ2pDLElBQUlNLGNBQWM7d0JBQ2hCaEIsVUFBVSxHQUFzQmdCLE9BQW5CQSxZQUFZLENBQUMsRUFBRSxFQUFDLEtBQWlDLE9BQTlCQSxZQUFZLENBQUMsRUFBRSxDQUFDQyxXQUFXO29CQUM3RDtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSwrREFBK0Q7UUFDL0QsSUFBSXBCLFVBQVUsS0FBS0QsYUFBYTtZQUM5QixNQUFNc0IsWUFBWXRCLFlBQVlXLFdBQVc7WUFDekMsTUFBTUUsV0FBV2IsWUFBWWMsS0FBSyxDQUFDO1lBQ25DLElBQUlELFVBQVVaLFFBQVFjLFNBQVNGLFFBQVEsQ0FBQyxFQUFFO1lBRTFDLE1BQU1HLFdBQVdoQixZQUFZYyxLQUFLLENBQUM7WUFDbkMsSUFBSUUsVUFBVWQsTUFBTSxHQUFlLE9BQVpjLFFBQVEsQ0FBQyxFQUFFLEVBQUM7WUFFbkMsTUFBTUMsZUFBZWpCLFlBQVljLEtBQUssQ0FBQztZQUN2QyxJQUFJRyxjQUFjO2dCQUNoQixNQUFNQyxjQUFjSSxVQUFVVixRQUFRLENBQUMsVUFBVSxTQUFTVSxVQUFVVixRQUFRLENBQUMsU0FBUyxRQUFRO2dCQUM5RlQsVUFBVSxHQUF5QmUsT0FBdEJELFlBQVksQ0FBQyxFQUFFLEVBQUMsUUFBa0IsT0FBWkMsYUFBY0MsSUFBSTtZQUN2RDtRQUNGO1FBRUEsT0FBTztZQUFFbEI7WUFBT0M7WUFBS0M7WUFBU0M7UUFBUTtJQUN4QztJQUVBLCtDQUErQztJQUMvQ3RFLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlGLG1CQUFtQjtZQUN2QixJQUFJO2dCQUNGckMsV0FBVztnQkFFWCx1REFBdUQ7Z0JBQ3ZELE1BQU1zQyxXQUFXLE1BQU1oRixvRUFBY0EsQ0FBQ2lGLFdBQVcsQ0FBQztnQkFDbERDLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEJIO2dCQUV0QyxJQUFJSSxjQUFjLEVBQUU7Z0JBQ3BCLElBQUlKLFNBQVNLLElBQUksSUFBSXhCLE1BQU1DLE9BQU8sQ0FBQ2tCLFNBQVNLLElBQUksR0FBRztvQkFDakRELGNBQWNKLFNBQVNLLElBQUk7Z0JBQzdCLE9BQU8sSUFBSUwsU0FBU0ssSUFBSSxJQUFJTCxTQUFTSyxJQUFJLENBQUNDLFFBQVEsSUFBSXpCLE1BQU1DLE9BQU8sQ0FBQ2tCLFNBQVNLLElBQUksQ0FBQ0MsUUFBUSxHQUFHO29CQUMzRkYsY0FBY0osU0FBU0ssSUFBSSxDQUFDQyxRQUFRO2dCQUN0QyxPQUFPLElBQUl6QixNQUFNQyxPQUFPLENBQUNrQixXQUFXO29CQUNsQ0ksY0FBY0o7Z0JBQ2hCO2dCQUVBLGlEQUFpRDtnQkFDakQsTUFBTU8sbUJBQW1CSCxZQUFZSSxHQUFHLENBQUNDLENBQUFBO29CQUN2QyxNQUFNQyxRQUFRcEMsb0JBQW9CbUMsSUFBSWxDLGNBQWMsRUFBRWtDLElBQUlqQyxXQUFXO29CQUNyRSxPQUFPO3dCQUNMbUMsSUFBSUYsSUFBSUcsR0FBRzt3QkFDWEEsS0FBS0gsSUFBSUcsR0FBRzt3QkFDWkMsTUFBTUosSUFBSUksSUFBSTt3QkFDZEMsT0FBT0wsSUFBSUssS0FBSzt3QkFDaEJyQyxPQUFPaUMsTUFBTWpDLEtBQUs7d0JBQ2xCQyxLQUFLZ0MsTUFBTWhDLEdBQUc7d0JBQ2RDLFNBQVMrQixNQUFNL0IsT0FBTzt3QkFDdEJDLFNBQVM4QixNQUFNOUIsT0FBTzt3QkFDdEJKLGFBQWFpQyxJQUFJakMsV0FBVzt3QkFDNUJELGdCQUFnQmtDLElBQUlsQyxjQUFjO29CQUNwQztnQkFDRjtnQkFFQW5CLFlBQVltRDtnQkFFWiw0Q0FBNEM7Z0JBQzVDLElBQUl4QyxVQUFVd0MsaUJBQWlCUSxNQUFNLEdBQUcsR0FBRztvQkFDekMsTUFBTUMsWUFBWVQsaUJBQWlCVSxJQUFJLENBQUNDLENBQUFBOzRCQUd0Q0EsV0FDQUE7K0JBSEFBLEtBQUtOLEdBQUcsS0FBSzdDLFVBQ2JtRCxLQUFLUCxFQUFFLEtBQUs1QyxVQUNabUQsRUFBQUEsWUFBQUEsS0FBS04sR0FBRyxjQUFSTSxnQ0FBQUEsVUFBVUMsUUFBUSxRQUFPcEQsVUFDekJtRCxFQUFBQSxXQUFBQSxLQUFLUCxFQUFFLGNBQVBPLCtCQUFBQSxTQUFTQyxRQUFRLFFBQU9wRDs7b0JBRTFCLElBQUlpRCxXQUFXO3dCQUNiN0MsZ0JBQWdCNkM7d0JBQ2hCZCxRQUFRQyxHQUFHLENBQUMsd0JBQXdCYTtvQkFDdEMsT0FBTzt3QkFDTGQsUUFBUXZDLEtBQUssQ0FBQywyQkFBMkJJO3dCQUN6Q21DLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JJLGlCQUFpQkMsR0FBRyxDQUFDWSxDQUFBQSxJQUFNO2dDQUFFVCxJQUFJUyxFQUFFVCxFQUFFO2dDQUFFQyxLQUFLUSxFQUFFUixHQUFHO2dDQUFFQyxNQUFNTyxFQUFFUCxJQUFJOzRCQUFDO3dCQUNoR2pELFNBQVM7b0JBQ1g7Z0JBQ0Y7WUFFRixFQUFFLE9BQU9ELE9BQU87Z0JBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLDZCQUE2QkE7Z0JBQzNDQyxTQUFTO2dCQUNUUixZQUFZLEVBQUU7Z0JBQ2RlLGdCQUFnQjtZQUNsQixTQUFVO2dCQUNSVCxXQUFXO1lBQ2I7UUFDRjtRQUVBLG1DQUFtQztRQUNuQyxNQUFNMkQsZ0JBQWdCO1lBQ3BCLElBQUk7Z0JBQ0ZuQixRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTUgsV0FBVyxNQUFNbEYsZ0VBQVVBLENBQUN3RyxTQUFTLENBQUM7Z0JBQzVDcEIsUUFBUUMsR0FBRyxDQUFDLHlCQUF5Qkg7Z0JBRXJDLElBQUl1QixTQUFTLEVBQUU7Z0JBQ2YsSUFBSXZCLFNBQVNLLElBQUksQ0FBQ0EsSUFBSSxJQUFJeEIsTUFBTUMsT0FBTyxDQUFDa0IsU0FBU0ssSUFBSSxDQUFDQSxJQUFJLEdBQUc7b0JBQzNEa0IsU0FBU3ZCLFNBQVNLLElBQUksQ0FBQ0EsSUFBSTtnQkFDN0IsT0FBTyxJQUFJTCxTQUFTSyxJQUFJLElBQUlMLFNBQVNLLElBQUksQ0FBQ2tCLE1BQU0sSUFBSTFDLE1BQU1DLE9BQU8sQ0FBQ2tCLFNBQVNLLElBQUksQ0FBQ2tCLE1BQU0sR0FBRztvQkFDdkZBLFNBQVN2QixTQUFTSyxJQUFJLENBQUNrQixNQUFNO2dCQUMvQjtnQkFFQSx3Q0FBd0M7Z0JBQ3hDLE1BQU1DLG9CQUFvQkQsT0FBT2YsR0FBRyxDQUFDaUIsQ0FBQUEsTUFBUTt3QkFDM0NkLElBQUljLElBQUlDLE9BQU8sSUFBSUQsSUFBSWQsRUFBRTt3QkFDekJFLE1BQU1ZLElBQUlaLElBQUk7d0JBQ2RyQyxhQUFhaUQsSUFBSWpELFdBQVc7d0JBQzVCbUQsTUFBTUYsSUFBSUcsTUFBTSxJQUFJO3dCQUNwQkMsU0FBU0osSUFBSUksT0FBTzt3QkFDcEJDLFVBQVVMLElBQUlLLFFBQVE7b0JBQ3hCO2dCQUVBNUIsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QnFCLGtCQUFrQlQsTUFBTSxFQUFFO2dCQUMvRHpELFlBQVlrRTtnQkFFWiw4QkFBOEI7Z0JBQzlCLElBQUlBLGtCQUFrQlQsTUFBTSxHQUFHLEdBQUc7b0JBQ2hDLE1BQU1nQixZQUFZUCxrQkFBa0JQLElBQUksQ0FBQ1EsQ0FBQUEsTUFDdkNBLElBQUlaLElBQUksQ0FBQzFCLFdBQVcsR0FBR0MsUUFBUSxDQUFDLGFBQ2hDcUMsSUFBSVosSUFBSSxDQUFDMUIsV0FBVyxHQUFHQyxRQUFRLENBQUMsYUFDN0JvQyxpQkFBaUIsQ0FBQyxFQUFFO29CQUN6QnRCLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBMEI0QixVQUFVbEIsSUFBSSxFQUFFa0IsVUFBVXBCLEVBQUU7b0JBQ2xFcUIsY0FBY0QsVUFBVXBCLEVBQUU7Z0JBQzVCO1lBQ0YsRUFBRSxPQUFPaEQsT0FBTztvQkFFa0JBO2dCQURoQ3VDLFFBQVF2QyxLQUFLLENBQUMsK0JBQStCQTtnQkFDN0N1QyxRQUFRdkMsS0FBSyxDQUFDLGtCQUFrQkEsRUFBQUEsa0JBQUFBLE1BQU1xQyxRQUFRLGNBQWRyQyxzQ0FBQUEsZ0JBQWdCMEMsSUFBSSxLQUFJMUMsTUFBTXNFLE9BQU87Z0JBQ3JFLHVDQUF1QztnQkFDdkMsTUFBTUMsaUJBQWlCO29CQUNyQjt3QkFBRXZCLElBQUk7d0JBQWdCRSxNQUFNO3dCQUFvQmMsTUFBTTtvQkFBUztvQkFDL0Q7d0JBQUVoQixJQUFJO3dCQUFnQkUsTUFBTTt3QkFBb0JjLE1BQU07b0JBQVM7b0JBQy9EO3dCQUFFaEIsSUFBSTt3QkFBWUUsTUFBTTt3QkFBWWMsTUFBTTtvQkFBUztvQkFDbkQ7d0JBQUVoQixJQUFJO3dCQUFhRSxNQUFNO3dCQUFhYyxNQUFNO29CQUFTO2lCQUN0RDtnQkFDRHJFLFlBQVk0RTtnQkFDWkYsY0FBYztZQUNoQjtRQUNGO1FBRUEsaUNBQWlDO1FBQ2pDLE1BQU1HLGVBQWU7WUFDbkIsSUFBSTtnQkFDRmpDLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixNQUFNSCxXQUFXLE1BQU1sRixnRUFBVUEsQ0FBQ3NILFVBQVUsQ0FBQztnQkFDN0NsQyxRQUFRQyxHQUFHLENBQUMsdUJBQXVCSDtnQkFFbkMsSUFBSXpDLFVBQVUsRUFBRTtnQkFDaEIsSUFBSXlDLFNBQVNLLElBQUksQ0FBQ0EsSUFBSSxJQUFJeEIsTUFBTUMsT0FBTyxDQUFDa0IsU0FBU0ssSUFBSSxDQUFDQSxJQUFJLEdBQUc7b0JBQzNEOUMsVUFBVXlDLFNBQVNLLElBQUksQ0FBQ0EsSUFBSTtnQkFDOUIsT0FBTyxJQUFJTCxTQUFTSyxJQUFJLElBQUlMLFNBQVNLLElBQUksQ0FBQzlDLE9BQU8sSUFBSXNCLE1BQU1DLE9BQU8sQ0FBQ2tCLFNBQVNLLElBQUksQ0FBQzlDLE9BQU8sR0FBRztvQkFDekZBLFVBQVV5QyxTQUFTSyxJQUFJLENBQUM5QyxPQUFPO2dCQUNqQztnQkFFQSx3Q0FBd0M7Z0JBQ3hDLE1BQU04RSxxQkFBcUI5RSxRQUFRaUQsR0FBRyxDQUFDOEIsQ0FBQUEsU0FBVzt3QkFDaEQzQixJQUFJMkIsT0FBT0MsVUFBVTt3QkFDckIxQixNQUFNeUIsT0FBT0UsVUFBVTt3QkFDdkJWLFVBQVVRLE9BQU9SLFFBQVE7b0JBQzNCO2dCQUVBNUIsUUFBUUMsR0FBRyxDQUFDLGlDQUF1QmtDLG1CQUFtQnRCLE1BQU0sRUFBRTtnQkFDOUR2RCxXQUFXNkU7Z0JBRVgsa0NBQWtDO2dCQUNsQyxJQUFJQSxtQkFBbUJ0QixNQUFNLEdBQUcsR0FBRztvQkFDakMsTUFBTTBCLGdCQUFnQkosbUJBQW1CcEIsSUFBSSxDQUFDcUIsQ0FBQUEsU0FDNUNBLE9BQU8zQixFQUFFLEtBQUssU0FDWDBCLGtCQUFrQixDQUFDLEVBQUU7b0JBQzFCbkMsUUFBUUMsR0FBRyxDQUFDLHdDQUE4QnNDLGNBQWM1QixJQUFJLEVBQUU0QixjQUFjOUIsRUFBRTtvQkFDOUUrQixvQkFBb0JELGNBQWM5QixFQUFFO2dCQUN0QztZQUNGLEVBQUUsT0FBT2hELE9BQU87b0JBRWtCQTtnQkFEaEN1QyxRQUFRdkMsS0FBSyxDQUFDLDZCQUE2QkE7Z0JBQzNDdUMsUUFBUXZDLEtBQUssQ0FBQyxrQkFBa0JBLEVBQUFBLGtCQUFBQSxNQUFNcUMsUUFBUSxjQUFkckMsc0NBQUFBLGdCQUFnQjBDLElBQUksS0FBSTFDLE1BQU1zRSxPQUFPO2dCQUNyRSx1Q0FBdUM7Z0JBQ3ZDLE1BQU1VLGdCQUFnQjtvQkFDcEI7d0JBQUVoQyxJQUFJO3dCQUFNRSxNQUFNO3dCQUFrQnJDLGFBQWE7d0JBQVdvRSxTQUFTO3dCQUFXQyxNQUFNO29CQUFZO29CQUNsRzt3QkFBRWxDLElBQUk7d0JBQWNFLE1BQU07d0JBQXlCckMsYUFBYTt3QkFBYW9FLFNBQVM7d0JBQWlCQyxNQUFNO29CQUFZO29CQUN6SDt3QkFBRWxDLElBQUk7d0JBQU1FLE1BQU07d0JBQWdCckMsYUFBYTt3QkFBYW9FLFNBQVM7d0JBQWFDLE1BQU07b0JBQVk7aUJBQ3JHO2dCQUNEckYsV0FBV21GO2dCQUNYRCxvQkFBb0I7WUFDdEI7UUFDRjtRQUVBLG1EQUFtRDtRQUNuRDNDO1FBQ0FzQjtRQUNBYztJQUdGLEdBQUc7UUFBQ3BFO0tBQU87SUFFWCxzQkFBc0I7SUFDdEIsTUFBTSxDQUFDK0UsWUFBWWQsY0FBYyxHQUFHM0gsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDMEksa0JBQWtCTCxvQkFBb0IsR0FBR3JJLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzJJLGdCQUFnQkMsa0JBQWtCLEdBQUc1SSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUM2SSxlQUFlQyxpQkFBaUIsR0FBRzlJLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQytJLGVBQWVDLGlCQUFpQixHQUFHaEosK0NBQVFBLENBQUM0RDtJQUNuRCxNQUFNLENBQUNxRixVQUFVQyxZQUFZLEdBQUdsSiwrQ0FBUUEsQ0FBQztJQUV6Qyw0QkFBNEI7SUFDNUIsTUFBTSxDQUFDbUosa0JBQWtCQyxvQkFBb0IsR0FBR3BKLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3FKLG1CQUFtQkMscUJBQXFCLEdBQUd0SiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUN1SixlQUFlQyxpQkFBaUIsR0FBR3hKLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3lKLGVBQWVDLGlCQUFpQixHQUFHMUosK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDMkosa0JBQWtCQyxvQkFBb0IsR0FBRzVKLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzZKLFlBQVlDLGNBQWMsR0FBRzlKLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQytKLEtBQUtDLE9BQU8sR0FBR2hLLCtDQUFRQSxDQUFDO0lBRS9CLDBCQUEwQjtJQUMxQixNQUFNLENBQUNpSyxVQUFVQyxZQUFZLEdBQUdsSywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNtSyxVQUFVQyxZQUFZLEdBQUdwSywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNxSyxjQUFjQyxnQkFBZ0IsR0FBR3RLLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3VLLGVBQWVDLGlCQUFpQixHQUFHeEssK0NBQVFBLENBQUM7SUFFbkQsK0JBQStCO0lBQy9CLE1BQU15SyxtQkFBbUIsQ0FBQ0M7UUFDeEIsSUFBSSxDQUFDQSxLQUFLO1lBQ1IsT0FBTztRQUNUO1FBQ0EsSUFBSUEsSUFBSWhFLE1BQU0sR0FBRyxHQUFHO1lBQ2xCLE9BQU87UUFDVDtRQUNBLElBQUksQ0FBQyxjQUFjaUUsSUFBSSxDQUFDRCxNQUFNO1lBQzVCLE9BQU87UUFDVDtRQUNBLElBQUksQ0FBQyxjQUFjQyxJQUFJLENBQUNELE1BQU07WUFDNUIsT0FBTztRQUNUO1FBQ0EsSUFBSSxDQUFDLFdBQVdDLElBQUksQ0FBQ0QsTUFBTTtZQUN6QixPQUFPO1FBQ1Q7UUFDQSxJQUFJLENBQUMsOENBQThDQyxJQUFJLENBQUNELE1BQU07WUFDNUQsT0FBTztRQUNUO1FBQ0EsT0FBTztJQUNUO0lBRUEsb0NBQW9DO0lBQ3BDLE1BQU1FLHlCQUF5QjtRQUM3QixNQUFNQyxZQUFZO1FBQ2xCLE1BQU1DLFlBQVk7UUFDbEIsTUFBTUMsVUFBVTtRQUNoQixNQUFNQyxVQUFVO1FBQ2hCLE1BQU1DLFdBQVdKLFlBQVlDLFlBQVlDLFVBQVVDO1FBRW5ELElBQUlFLGNBQWM7UUFFbEIsbURBQW1EO1FBQ25EQSxlQUFlTCxTQUFTLENBQUNNLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLUixVQUFVbkUsTUFBTSxFQUFFO1FBQ3RFd0UsZUFBZUosU0FBUyxDQUFDSyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS1AsVUFBVXBFLE1BQU0sRUFBRTtRQUN0RXdFLGVBQWVILE9BQU8sQ0FBQ0ksS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtOLFFBQVFyRSxNQUFNLEVBQUU7UUFDbEV3RSxlQUFlRixPQUFPLENBQUNHLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLTCxRQUFRdEUsTUFBTSxFQUFFO1FBRWxFLHlCQUF5QjtRQUN6QixJQUFLLElBQUk0RSxJQUFJLEdBQUdBLElBQUksSUFBSUEsSUFBSztZQUMzQkosZUFBZUQsUUFBUSxDQUFDRSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS0osU0FBU3ZFLE1BQU0sRUFBRTtRQUN0RTtRQUVBLHVCQUF1QjtRQUN2QndFLGNBQWNBLFlBQVlLLEtBQUssQ0FBQyxJQUFJQyxJQUFJLENBQUMsSUFBTUwsS0FBS0UsTUFBTSxLQUFLLEtBQUtJLElBQUksQ0FBQztRQUV6RXJCLFlBQVljO1FBQ1pWLGlCQUFpQjtJQUNuQjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNa0IsdUJBQXVCLENBQUNDO1FBQzVCLE1BQU1ULGNBQWNTLEVBQUVDLE1BQU0sQ0FBQ2hILEtBQUs7UUFDbEN3RixZQUFZYztRQUNaVixpQkFBaUJDLGlCQUFpQlM7SUFDcEM7SUFFQSwrQ0FBK0M7SUFDL0NqTCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ2tLLFVBQVU7WUFDYlM7UUFDRjtJQUNGLEdBQUcsRUFBRTtJQUVMLDRCQUE0QjtJQUM1QixNQUFNaUIsa0JBQWtCO1FBQ3RCLElBQUksQ0FBQ2hJLGNBQWM7WUFDakJoRCxpREFBS0EsQ0FBQ3lDLEtBQUssQ0FBQztZQUNaO1FBQ0Y7UUFFQSxvQkFBb0I7UUFDcEIsTUFBTXdJLDBCQUEwQnJCLGlCQUFpQk47UUFDakQsSUFBSTJCLHlCQUF5QjtZQUMzQnRCLGlCQUFpQnNCO1lBQ2pCakwsaURBQUtBLENBQUN5QyxLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtnQkE4Q1VPLHlCQThDUjhCLHFCQUFBQTtZQTNGSmxDLGdCQUFnQjtZQUVoQixzQ0FBc0M7WUFDdEMsTUFBTXNJLFlBQVlsSSxhQUFhMEMsR0FBRyxJQUFJMUMsYUFBYXlDLEVBQUU7WUFFckQsSUFBSSxDQUFDeUYsV0FBVztnQkFDZGxMLGlEQUFLQSxDQUFDeUMsS0FBSyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxnREFBZ0Q7WUFDaEQsTUFBTTBJLG1CQUFtQjtnQkFDdkIsVUFBVTtnQkFDVixNQUFNO2dCQUNOLFdBQVc7Z0JBQ1gsY0FBYztnQkFDZCxPQUFPO2dCQUNQLE1BQU07Z0JBQ04sYUFBYTtnQkFDYixRQUFRO1lBQ1Y7WUFFQSxNQUFNQyxlQUFlO2dCQUNuQixnQkFBZ0I7Z0JBQ2hCLGdCQUFnQjtnQkFDaEIsZ0JBQWdCO2dCQUNoQixZQUFZO2dCQUNaLFlBQVk7Z0JBQ1osYUFBYTtnQkFDYixhQUFhO2dCQUNiLGdCQUFnQjtnQkFDaEIsZ0JBQWdCO1lBQ2xCO1lBRUEsd0NBQXdDO1lBQ3hDLE1BQU1DLGNBQWMsR0FBd0JDLE9BQXJCdEksYUFBYTJDLElBQUksRUFBQyxLQUFjLE9BQVgyRixLQUFLQyxHQUFHO1lBRXBELHlFQUF5RTtZQUN6RSxNQUFNQyxXQUFXO2dCQUNmTixXQUFXQTtnQkFDWDlDLFVBQVVBO2dCQUNWcUQsUUFBUTNELG1CQUFtQixZQUFZLElBQUlBLG1CQUFtQixZQUFZLElBQUk7Z0JBQzlFLCtDQUErQztnQkFDL0M0RCxxQkFBcUI7b0JBQ25CLHFCQUFxQjtvQkFDckI3SSxRQUFRRyxFQUFBQSwwQkFBQUEsYUFBYTJJLFNBQVMsY0FBdEIzSSw4Q0FBQUEsd0JBQXdCNEksaUJBQWlCLEtBQUk1SSxhQUFheUMsRUFBRTtvQkFDcEVtQixVQUFVO29CQUNWUSxRQUFRK0QsZ0JBQWdCLENBQUN0RCxpQkFBaUIsSUFBSTtvQkFDOUNnRSxpQkFBaUJULFlBQVksQ0FBQ3hELFdBQVcsSUFBSUE7b0JBQzdDeUQsYUFBYUE7b0JBQ2JqQyxVQUFVQTtvQkFDVjBDLGNBQWN4QztvQkFDZHlDLFNBQVMsRUFBRTtvQkFDWEMsVUFBVTtvQkFDVkMsUUFBUTt3QkFDTkMsbUJBQW1CMUQsc0JBQXNCO3dCQUN6QzJELFlBQVk3RCxxQkFBcUI7d0JBQ2pDVSxZQUFZQSxlQUFlO29CQUM3QjtvQkFDQSxvQ0FBb0M7b0JBQ3BDb0QsS0FBS3BKLGFBQWFPLEtBQUssSUFBSVAsYUFBYW9KLEdBQUc7b0JBQzNDNUksS0FBS1IsYUFBYVEsR0FBRztvQkFDckJDLFNBQVNULGFBQWFTLE9BQU87b0JBQzdCNEksV0FBV3JKLGFBQWFVLE9BQU8sSUFBSVYsYUFBYXFKLFNBQVM7b0JBQ3pELHVDQUF1QztvQkFDdkNDLGdCQUFnQjt3QkFDZFQsaUJBQWlCakU7d0JBQ2pCMkUsVUFBVTFFO3dCQUNWRyxlQUFlQTt3QkFDZndFLFFBQVF0SixnQkFBZ0JnRjt3QkFDeEJ1RSxVQUFVekosYUFBYTJDLElBQUk7d0JBQzNCMkMsa0JBQWtCQTt3QkFDbEJFLG1CQUFtQkE7d0JBQ25CRSxlQUFlQTt3QkFDZkUsZUFBZUE7d0JBQ2ZFLGtCQUFrQkE7d0JBQ2xCRSxZQUFZQTt3QkFDWkUsS0FBS0E7b0JBQ1A7Z0JBQ0Y7WUFDRjtZQUVBbEUsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QnVHO1lBQ25DeEcsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQmpDO1lBRTlCLG1DQUFtQztZQUNuQyxNQUFNOEIsV0FBVyxNQUFNakYsaUVBQVdBLENBQUM2TSxhQUFhLENBQUNsQjtZQUVqRHhHLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JIO1lBRTlCLHNDQUFzQztZQUN0QyxLQUFJQSxpQkFBQUEsU0FBU0ssSUFBSSxjQUFiTCxzQ0FBQUEsc0JBQUFBLGVBQWU2SCxJQUFJLGNBQW5CN0gsMENBQUFBLG9CQUFxQjhILFNBQVMsRUFBRTtnQkFDbEM1SyxhQUFhOEMsU0FBU0ssSUFBSSxDQUFDd0gsSUFBSSxDQUFDQyxTQUFTO1lBQzNDO1lBRUEsZ0NBQWdDO1lBQ2hDNU0saURBQUtBLENBQUM2TSxPQUFPLENBQUMsR0FBcUIsT0FBbEI3SixhQUFhMkMsSUFBSSxFQUFDO1lBRW5DLDJCQUEyQjtZQUMzQjdELE9BQU9nTCxJQUFJLENBQUM7UUFFZCxFQUFFLE9BQU9ySyxPQUFPO2dCQUdlQSxpQkFHekJBLHNCQUFBQSxrQkFFT0Esa0JBRUFBLGtCQUdBQTtZQVpYdUMsUUFBUXZDLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDdUMsUUFBUXZDLEtBQUssQ0FBQyxtQkFBbUJBLE1BQU1xQyxRQUFRO1lBQy9DRSxRQUFRdkMsS0FBSyxDQUFDLGdCQUFlQSxrQkFBQUEsTUFBTXFDLFFBQVEsY0FBZHJDLHNDQUFBQSxnQkFBZ0IwQyxJQUFJO1lBRWpELGtDQUFrQztZQUNsQyxLQUFJMUMsbUJBQUFBLE1BQU1xQyxRQUFRLGNBQWRyQyx3Q0FBQUEsdUJBQUFBLGlCQUFnQjBDLElBQUksY0FBcEIxQywyQ0FBQUEscUJBQXNCc0UsT0FBTyxFQUFFO2dCQUNqQy9HLGlEQUFLQSxDQUFDeUMsS0FBSyxDQUFDQSxNQUFNcUMsUUFBUSxDQUFDSyxJQUFJLENBQUM0QixPQUFPO1lBQ3pDLE9BQU8sSUFBSXRFLEVBQUFBLG1CQUFBQSxNQUFNcUMsUUFBUSxjQUFkckMsdUNBQUFBLGlCQUFnQnNLLE1BQU0sTUFBSyxLQUFLO2dCQUN6Qy9NLGlEQUFLQSxDQUFDeUMsS0FBSyxDQUFDO1lBQ2QsT0FBTyxJQUFJQSxFQUFBQSxtQkFBQUEsTUFBTXFDLFFBQVEsY0FBZHJDLHVDQUFBQSxpQkFBZ0JzSyxNQUFNLE1BQUssS0FBSztnQkFDekMvTSxpREFBS0EsQ0FBQ3lDLEtBQUssQ0FBQztnQkFDWlgsT0FBT2dMLElBQUksQ0FBQztZQUNkLE9BQU8sSUFBSXJLLEVBQUFBLG1CQUFBQSxNQUFNcUMsUUFBUSxjQUFkckMsdUNBQUFBLGlCQUFnQnNLLE1BQU0sTUFBSyxLQUFLO2dCQUN6Qy9NLGlEQUFLQSxDQUFDeUMsS0FBSyxDQUFDO1lBQ2QsT0FBTztnQkFDTHpDLGlEQUFLQSxDQUFDeUMsS0FBSyxDQUFDO1lBQ2Q7UUFDRixTQUFVO1lBQ1JHLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsMERBQTBEO0lBQzFELE1BQU1qQixtQkFBbUJRLFNBQVMwRCxNQUFNLEdBQUcsSUFBSTFELFNBQVNtRCxHQUFHLENBQUMwSCxDQUFBQSxLQUFPO1lBQ2pFdkgsSUFBSXVILEdBQUd2SCxFQUFFO1lBQ1RFLE1BQU1xSCxHQUFHckgsSUFBSTtZQUNic0gsTUFBTUMsVUFBVUYsR0FBR3ZHLElBQUksSUFBSXVHLEdBQUd0RyxNQUFNO1lBQ3BDRCxNQUFNdUcsR0FBR3ZHLElBQUksSUFBSXVHLEdBQUd0RyxNQUFNLElBQUk7WUFDOUJwRCxhQUFhMEosR0FBRzFKLFdBQVc7WUFDM0JxRCxTQUFTcUcsR0FBR3JHLE9BQU87WUFDbkJDLFVBQVVvRyxHQUFHcEcsUUFBUTtRQUN2QixNQUFNO1FBQ0o7WUFBRW5CLElBQUk7WUFBZ0JFLE1BQU07WUFBb0JzSCxNQUFNbE07WUFBWTBGLE1BQU07UUFBUTtRQUNoRjtZQUFFaEIsSUFBSTtZQUFnQkUsTUFBTTtZQUFvQnNILE1BQU1sTTtZQUFZMEYsTUFBTTtRQUFRO1FBQ2hGO1lBQUVoQixJQUFJO1lBQVlFLE1BQU07WUFBWXNILE1BQU0xTDtZQUFZa0YsTUFBTTtRQUFRO1FBQ3BFO1lBQUVoQixJQUFJO1lBQWFFLE1BQU07WUFBYXNILE1BQU16TDtZQUFZaUYsTUFBTTtRQUFRO0tBQ3ZFO0lBRUQsK0NBQStDO0lBQy9DLFNBQVN5RyxVQUFVeEcsTUFBTTtRQUN2QixNQUFNeUcsVUFBVTtZQUNkLFVBQVVwTTtZQUNWLFVBQVVRO1lBQ1YsVUFBVUM7WUFDVixXQUFXQztZQUNYLFNBQVNWLFdBQVcsb0JBQW9CO1FBQzFDO1FBRUEsNkNBQTZDO1FBQzdDLElBQUkyRixVQUFVLE9BQU9BLFdBQVcsVUFBVTtZQUN4QyxNQUFNMEcsWUFBWTFHLE9BQU96QyxXQUFXO1lBQ3BDLElBQUltSixVQUFVbEosUUFBUSxDQUFDLFdBQVcsT0FBT25EO1lBQ3pDLElBQUlxTSxVQUFVbEosUUFBUSxDQUFDLFdBQVcsT0FBTzNDO1lBQ3pDLElBQUk2TCxVQUFVbEosUUFBUSxDQUFDLFdBQVcsT0FBTzFDO1lBQ3pDLElBQUk0TCxVQUFVbEosUUFBUSxDQUFDLFlBQVksT0FBT3pDO1FBQzVDO1FBRUEsT0FBTzBMLE9BQU8sQ0FBQ3pHLE9BQU8sSUFBSTNGO0lBQzVCO0lBRUEsd0RBQXdEO0lBQ3hELE1BQU1hLFlBQVlTLFFBQVF3RCxNQUFNLEdBQUcsSUFBSXhELFFBQVFpRCxHQUFHLENBQUM4QixDQUFBQSxTQUFXO1lBQzVEM0IsSUFBSTJCLE9BQU8zQixFQUFFO1lBQ2JFLE1BQU15QixPQUFPekIsSUFBSTtZQUNqQjBILE1BQU1DLGNBQWNsRyxPQUFPTSxPQUFPO1lBQ2xDNkYsTUFBTUMsY0FBY3BHLE9BQU8zQixFQUFFO1lBQzdCbkMsYUFBYThELE9BQU85RCxXQUFXO1lBQy9CcUUsTUFBTVAsT0FBT08sSUFBSTtZQUNqQkQsU0FBU04sT0FBT00sT0FBTztRQUN6QixNQUFNO1FBQ0o7WUFBRWpDLElBQUk7WUFBTUUsTUFBTTtZQUFrQjBILE1BQU07WUFBUUUsTUFBTTtZQUFRakssYUFBYTtZQUFXcUUsTUFBTTtZQUFhRCxTQUFTO1FBQVU7UUFDOUg7WUFBRWpDLElBQUk7WUFBY0UsTUFBTTtZQUF5QjBILE1BQU07WUFBUUUsTUFBTTtZQUFTakssYUFBYTtZQUFhcUUsTUFBTTtZQUFhRCxTQUFTO1FBQWdCO1FBQ3RKO1lBQUVqQyxJQUFJO1lBQU1FLE1BQU07WUFBZ0IwSCxNQUFNO1lBQVFFLE1BQU07WUFBU2pLLGFBQWE7WUFBYXFFLE1BQU07WUFBYUQsU0FBUztRQUFZO0tBQ2xJO0lBRUQsc0NBQXNDO0lBQ3RDLFNBQVM0RixjQUFjNUYsT0FBTztRQUM1QixNQUFNK0YsVUFBVTtZQUNkLFdBQVc7WUFDWCxpQkFBaUI7WUFDakIsYUFBYTtZQUNiLFVBQVU7WUFDVixlQUFlO1FBQ2pCO1FBQ0EsT0FBT0EsT0FBTyxDQUFDL0YsUUFBUSxJQUFJO0lBQzdCO0lBRUEsU0FBUzhGLGNBQWNFLFFBQVE7UUFDN0IsTUFBTUMsVUFBVTtZQUNkLE1BQU07WUFDTixjQUFjO1lBQ2QsV0FBVztZQUNYLFdBQVc7WUFDWCxNQUFNO1FBQ1I7UUFDQSxPQUFPQSxPQUFPLENBQUNELFNBQVMsSUFBSTtJQUM5QjtJQUlBLE1BQU1FLGlCQUFpQjtRQUNyQixJQUFJLENBQUM1SyxjQUFjLE9BQU87UUFDMUIsSUFBSTZLLFFBQVE3SyxhQUFhNEMsS0FBSztRQUU5QixzQkFBc0I7UUFDdEJpSSxTQUFTN0YsZ0JBQWdCLElBQUksMkJBQTJCO1FBRXhELDZDQUE2QztRQUM3QyxJQUFJLENBQUM5RSxnQkFBZ0JnRixlQUFlO1lBQ2xDMkYsU0FBUyxJQUFJLG9CQUFvQjtRQUNuQztRQUVBLG1DQUFtQztRQUNuQyxJQUFJdkYscUJBQXFCLFFBQVE7WUFDL0J1RixTQUFTLElBQUksMkJBQTJCO1FBQzFDO1FBRUEsMEJBQTBCO1FBQzFCLElBQUlyRixzQkFBc0IsV0FBVztZQUNuQ3FGLFNBQVMsSUFBSSxtQkFBbUI7UUFDbEM7UUFFQSxpQ0FBaUM7UUFDakMsSUFBSW5GLGtCQUFrQixHQUFHO1lBQ3ZCbUYsU0FBUyxJQUFJLDJCQUEyQjtRQUMxQztRQUVBLHNCQUFzQjtRQUN0QixNQUFNQyxxQkFBcUI7WUFDekIsU0FBUztZQUNULFNBQVM7WUFDVCxTQUFTO1lBQ1QsT0FBTztRQUNUO1FBQ0EsSUFBSWxGLGtCQUFrQixVQUFVa0Ysa0JBQWtCLENBQUNsRixjQUFjLEVBQUU7WUFDakVpRixTQUFTQyxrQkFBa0IsQ0FBQ2xGLGNBQWM7UUFDNUM7UUFFQSx5QkFBeUI7UUFDekIsSUFBSUUscUJBQXFCLFdBQVc7WUFDbEMrRSxTQUFTLE1BQU0saUNBQWlDO1FBQ2xEO1FBRUEsa0JBQWtCO1FBQ2xCLElBQUk3RSxlQUFlLFFBQVE7WUFDekI2RSxTQUFTLEtBQUssd0NBQXdDO1FBQ3hEO1FBRUEsZ0VBQWdFO1FBQ2hFLE1BQU1FLFdBQVc7WUFDZixTQUFTO1lBQ1QsWUFBWSxJQUFJLDJDQUEyQztRQUM3RDtRQUNBLElBQUk3RSxRQUFRLFVBQVU2RSxRQUFRLENBQUM3RSxJQUFJLEVBQUU7WUFDbkMyRSxTQUFTRSxRQUFRLENBQUM3RSxJQUFJO1FBQ3hCO1FBRUEsaUJBQWlCO1FBQ2pCMkUsU0FBU3pGO1FBRVQsb0JBQW9CO1FBQ3BCLE1BQU00RixjQUFjO1lBQ2xCQyxTQUFTO1lBQ1QsV0FBVyxJQUFJO1lBQ2ZDLFVBQVUsS0FBSyxLQUFPLGVBQWU7UUFDdkM7UUFFQSxPQUFPTCxRQUFRRyxXQUFXLENBQUNsRyxlQUFlO0lBQzVDO0lBSUEsZ0JBQWdCO0lBQ2hCLElBQUl2RixTQUFTO1FBQ1gscUJBQ0UsOERBQUN0QjtZQUFJRCxXQUFVO3NCQUNiLDRFQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7a0NBQ2YsOERBQUN4QixnRUFBVUE7d0JBQUN3QixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNOUM7SUFFQSxjQUFjO0lBQ2QsSUFBSXlCLE9BQU87UUFDVCxxQkFDRSw4REFBQ3hCO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ3hCLGdFQUFVQTt3QkFBQzJPLFNBQVE7d0JBQUtuTixXQUFVO2tDQUFvQjs7Ozs7O2tDQUd2RCw4REFBQ3hCLGdFQUFVQTt3QkFBQ3dCLFdBQVU7a0NBQ25CeUI7Ozs7OztrQ0FFSCw4REFBQ2hELDREQUFNQTt3QkFDTDJPLFNBQVMsSUFBTUMsT0FBTzlCLFFBQVEsQ0FBQytCLE1BQU07d0JBQ3JDQyxPQUFNO3dCQUNOQyxNQUFLO2tDQUNOOzs7Ozs7Ozs7Ozs7Ozs7OztJQU1UO0lBRUEscUJBQ0UsOERBQUN2TjtRQUFJRCxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ3ZCLDREQUFNQTt3Q0FDTDBPLFNBQVE7d0NBQ1JLLE1BQUs7d0NBQ0xKLFNBQVMsSUFBTUMsT0FBT0ksT0FBTyxDQUFDQyxJQUFJO3dDQUNsQzFOLFdBQVU7OzBEQUVWLDhEQUFDUixpT0FBYUE7Z0RBQUNRLFdBQVU7Ozs7OzswREFDekIsOERBQUMyTjtnREFBSzNOLFdBQVU7MERBQW9CZSxFQUFFOzs7Ozs7Ozs7Ozs7a0RBRXhDLDhEQUFDZDt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUN4QixnRUFBVUE7Z0RBQUMyTyxTQUFRO2dEQUFLbk4sV0FBVTswREFDaENlLEVBQUU7Ozs7OzswREFFTCw4REFBQ3ZDLGdFQUFVQTtnREFBQ3dCLFdBQVU7MERBQ25CZ0MsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjMkMsSUFBSSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSTdCLDhEQUFDMUU7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDeEIsZ0VBQVVBO3dDQUFDd0IsV0FBVTtrREFBb0NlLEVBQUU7Ozs7OztrREFDNUQsOERBQUN2QyxnRUFBVUE7d0NBQUMyTyxTQUFRO3dDQUFLbk4sV0FBVTs7NENBQ2hDZ0MsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjNEMsS0FBSyxLQUFJOzRDQUFFOzRDQUFLN0QsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzNDLDhEQUFDZDtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBR2IsOERBQUN0QiwwREFBSUE7b0NBQUNzQixXQUFVOzhDQUNkLDRFQUFDckIsOERBQVFBO3dDQUFDcUIsV0FBVTs7MERBQ2xCLDhEQUFDQztnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUNiLDRFQUFDZixpT0FBVUE7NERBQUNlLFdBQVU7Ozs7Ozs7Ozs7O2tFQUV4Qiw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNiLDhEQUFDeEIsZ0VBQVVBO2dFQUFDMk8sU0FBUTtnRUFBS25OLFdBQVU7MEVBQ2hDZ0MsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjMkMsSUFBSSxLQUFJOzs7Ozs7NERBRXhCekMsOEJBQ0MsOERBQUNqQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNYLGlPQUFVQTt3RUFBQ1csV0FBVTs7Ozs7O2tGQUN0Qiw4REFBQzJOO3dFQUFLM04sV0FBVTtrRkFBZ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNeEUsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNiLDhEQUFDZCxpT0FBT0E7Z0VBQUNjLFdBQVU7Ozs7OzswRUFDbkIsOERBQUNDO2dFQUFJRCxXQUFVOzBFQUFvQ2UsRUFBRTs7Ozs7OzBFQUNyRCw4REFBQ2Q7Z0VBQUlELFdBQVU7MEVBQWdEZ0MsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjTyxLQUFLLEtBQUk7Ozs7Ozs7Ozs7OztrRUFFeEYsOERBQUN0Qzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNDO2dFQUFJRCxXQUFVOzs7Ozs7MEVBQ2YsOERBQUNDO2dFQUFJRCxXQUFVOzBFQUFvQ2UsRUFBRTs7Ozs7OzBFQUNyRCw4REFBQ2Q7Z0VBQUlELFdBQVU7MEVBQWdEZ0MsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjUSxHQUFHLEtBQUk7Ozs7Ozs7Ozs7OztrRUFFdEYsOERBQUN2Qzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNiLGlPQUFhQTtnRUFBQ2EsV0FBVTs7Ozs7OzBFQUN6Qiw4REFBQ0M7Z0VBQUlELFdBQVU7MEVBQW9DZSxFQUFFOzs7Ozs7MEVBQ3JELDhEQUFDZDtnRUFBSUQsV0FBVTswRUFBZ0RnQyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNTLE9BQU8sS0FBSTs7Ozs7Ozs7Ozs7O2tFQUUxRiw4REFBQ3hDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ1osaU9BQVNBO2dFQUFDWSxXQUFVOzs7Ozs7MEVBQ3JCLDhEQUFDQztnRUFBSUQsV0FBVTswRUFBb0NlLEVBQUU7Ozs7OzswRUFDckQsOERBQUNkO2dFQUFJRCxXQUFVOzBFQUFnRGdDLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY1UsT0FBTyxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPaEcsOERBQUNoRSwwREFBSUE7b0NBQUNzQixXQUFVOzhDQUNkLDRFQUFDckIsOERBQVFBO3dDQUFDcUIsV0FBVTs7MERBQ2xCLDhEQUFDeEIsZ0VBQVVBO2dEQUFDMk8sU0FBUTtnREFBS25OLFdBQVU7O29EQUEwRDtvREFDdkZlLEVBQUU7Ozs7Ozs7MERBRVIsOERBQUN2QyxnRUFBVUE7Z0RBQUN3QixXQUFVOzBEQUNuQmUsRUFBRTs7Ozs7OzBEQUVMLDhEQUFDZDtnREFBSUQsV0FBVTswREFDWjtvREFDQzt3REFBRXlFLElBQUk7d0RBQVdtSixPQUFPO3dEQUFVQyxVQUFVO3dEQUFJakosT0FBTzVDLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBYzRDLEtBQUssS0FBSTtvREFBRTtvREFDaEY7d0RBQUVILElBQUk7d0RBQVdtSixPQUFPO3dEQUFVQyxVQUFVO3dEQUFtQmpKLE9BQU8wRSxLQUFLd0UsS0FBSyxDQUFDLENBQUM5TCxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWM0QyxLQUFLLEtBQUksS0FBSyxJQUFJO29EQUFNO29EQUN4SDt3REFBRUgsSUFBSTt3REFBWW1KLE9BQU87d0RBQVdDLFVBQVU7d0RBQW9CakosT0FBTzBFLEtBQUt3RSxLQUFLLENBQUMsQ0FBQzlMLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBYzRDLEtBQUssS0FBSSxLQUFLLEtBQUs7b0RBQU07aURBQzdILENBQUNOLEdBQUcsQ0FBQyxDQUFDbUcsdUJBQ0wsOERBQUN4Szt3REFFQ21OLFNBQVMsSUFBTXJHLGtCQUFrQjBELE9BQU9oRyxFQUFFO3dEQUMxQ3pFLFdBQVcsNkVBSVYsT0FIQzhHLG1CQUFtQjJELE9BQU9oRyxFQUFFLEdBQ3hCLCtCQUNBO2tFQUdOLDRFQUFDeEU7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDQztvRUFBSUQsV0FBVTs7c0ZBQ2IsOERBQUNDOzRFQUFJRCxXQUFXLCtDQUVmLE9BREM4RyxtQkFBbUIyRCxPQUFPaEcsRUFBRSxHQUFHLGdDQUFnQztzRkFFOURxQyxtQkFBbUIyRCxPQUFPaEcsRUFBRSxrQkFDM0IsOERBQUN4RTtnRkFBSUQsV0FBVTs7Ozs7Ozs7Ozs7c0ZBR25CLDhEQUFDQzs0RUFBSUQsV0FBVTs7OEZBQ2IsOERBQUNDO29GQUFJRCxXQUFVOzhGQUFrRHlLLE9BQU9tRCxLQUFLOzs7Ozs7Z0ZBQzVFbkQsT0FBT29ELFFBQVEsa0JBQ2QsOERBQUM1TjtvRkFBSUQsV0FBVTs4RkFBaUR5SyxPQUFPb0QsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQUlyRiw4REFBQzVOO29FQUFJRCxXQUFVOztzRkFDYiw4REFBQ0M7NEVBQUlELFdBQVU7O2dGQUFnRHlLLE9BQU83RixLQUFLO2dGQUFDOzs7Ozs7O3NGQUM1RSw4REFBQzNFOzRFQUFJRCxXQUFVO3NGQUNaeUssT0FBT2hHLEVBQUUsS0FBSyxZQUFZLFVBQVVnRyxPQUFPaEcsRUFBRSxLQUFLLFlBQVksWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VEQTNCNUVnRyxPQUFPaEcsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQXNDeEIsOERBQUMvRiwwREFBSUE7b0NBQUNzQixXQUFVOzhDQUNkLDRFQUFDckIsOERBQVFBO3dDQUFDcUIsV0FBVTs7MERBQ2xCLDhEQUFDeEIsZ0VBQVVBO2dEQUFDMk8sU0FBUTtnREFBS25OLFdBQVU7O29EQUEwRDtvREFDdkZlLEVBQUU7Ozs7Ozs7MERBRVIsOERBQUN2QyxnRUFBVUE7Z0RBQUN3QixXQUFVOzBEQUNuQmUsRUFBRTs7Ozs7OzRDQUVKSSxTQUFTMEQsTUFBTSxLQUFLLGtCQUNuQiw4REFBQzVFO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVU7a0VBQWdCOzs7Ozs7a0VBQy9CLDhEQUFDQzt3REFBSUQsV0FBVTs7NERBQTZCOzREQUFFVyxpQkFBaUJrRSxNQUFNOzREQUFDOzs7Ozs7O2tFQUN0RSw4REFBQ2tKO3dEQUNDWCxTQUFTOzREQUNQcEosUUFBUUMsR0FBRyxDQUFDOzREQUNaLElBQUk7Z0VBQ0YsTUFBTUgsV0FBVyxNQUFNbEYsZ0VBQVVBLENBQUN3RyxTQUFTLENBQUM7Z0VBQzVDcEIsUUFBUUMsR0FBRyxDQUFDLHlCQUF5Qkg7NERBQ3ZDLEVBQUUsT0FBT3JDLE9BQU87Z0VBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLHdCQUF3QkE7NERBQ3hDO3dEQUNGO3dEQUNBekIsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7cUVBS0gsOERBQUNDO2dEQUFJRCxXQUFVOzBEQUNiLDRFQUFDQztvREFBSUQsV0FBVTs7d0RBQXlCO3dEQUFHbUIsU0FBUzBELE1BQU07d0RBQUM7Ozs7Ozs7Ozs7OzswREFHL0QsOERBQUM1RTtnREFBSUQsV0FBVTswREFDWlcsaUJBQWlCMkQsR0FBRyxDQUFDLENBQUMwSCxtQkFDckIsOERBQUMvTDt3REFFQ21OLFNBQVMsSUFBTXRILGNBQWNrRyxHQUFHdkgsRUFBRTt3REFDbEN6RSxXQUFXLHNGQUlWLE9BSEM0RyxlQUFlb0YsR0FBR3ZILEVBQUUsR0FDaEIsK0JBQ0E7a0VBR04sNEVBQUN4RTs0REFBSUQsV0FBVTs7OEVBQ2IsOERBQUNnTSxHQUFHQyxJQUFJO29FQUFDak0sV0FBVTs7Ozs7OzhFQUNuQiw4REFBQ0M7b0VBQUlELFdBQVU7O3NGQUNiLDhEQUFDQzs0RUFBSUQsV0FBVTtzRkFBOERnTSxHQUFHckgsSUFBSTs7Ozs7O3NGQUNwRiw4REFBQzFFOzRFQUFJRCxXQUFVO3NGQUNaZ00sR0FBR3ZHLElBQUksS0FBSyxVQUFVLHVCQUF1Qjs7Ozs7Ozs7Ozs7O2dFQUdqRG1CLGVBQWVvRixHQUFHdkgsRUFBRSxrQkFDbkIsOERBQUNsRixpT0FBU0E7b0VBQUNTLFdBQVU7Ozs7Ozs7Ozs7Ozt1REFqQnBCZ00sR0FBR3ZILEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0EyQnBCLDhEQUFDL0YsMERBQUlBO29DQUFDc0IsV0FBVTs4Q0FDZCw0RUFBQ3JCLDhEQUFRQTt3Q0FBQ3FCLFdBQVU7OzBEQUNsQiw4REFBQ3hCLGdFQUFVQTtnREFBQzJPLFNBQVE7Z0RBQUtuTixXQUFVOztvREFBMEQ7b0RBQ3ZGZSxFQUFFOzs7Ozs7OzBEQUVSLDhEQUFDdkMsZ0VBQVVBO2dEQUFDd0IsV0FBVTswREFDbkJlLEVBQUU7Ozs7Ozs0Q0FFSk0sUUFBUXdELE1BQU0sS0FBSyxrQkFDbEIsOERBQUM1RTtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUFnQjs7Ozs7O2tFQUMvQiw4REFBQ0M7d0RBQUlELFdBQVU7OzREQUE2Qjs0REFBRVksVUFBVWlFLE1BQU07NERBQUM7Ozs7Ozs7a0VBQy9ELDhEQUFDa0o7d0RBQ0NYLFNBQVM7NERBQ1BwSixRQUFRQyxHQUFHLENBQUM7NERBQ1osSUFBSTtnRUFDRixNQUFNSCxXQUFXLE1BQU1sRixnRUFBVUEsQ0FBQ3NILFVBQVUsQ0FBQztnRUFDN0NsQyxRQUFRQyxHQUFHLENBQUMsaUNBQWlDSDs0REFDL0MsRUFBRSxPQUFPckMsT0FBTztnRUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsZ0NBQWdDQTs0REFDaEQ7d0RBQ0Y7d0RBQ0F6QixXQUFVO2tFQUNYOzs7Ozs7Ozs7OztxRUFLSCw4REFBQ0M7Z0RBQUlELFdBQVU7MERBQ2IsNEVBQUNDO29EQUFJRCxXQUFVOzt3REFBeUI7d0RBQUdxQixRQUFRd0QsTUFBTTt3REFBQzs7Ozs7Ozs7Ozs7OzBEQUc5RCw4REFBQzVFO2dEQUFJRCxXQUFVOzBEQUNaWSxVQUFVMEQsR0FBRyxDQUFDLENBQUNpSCx5QkFDZCw4REFBQ3RMO3dEQUVDbU4sU0FBUyxJQUFNNUcsb0JBQW9CK0UsU0FBUzlHLEVBQUU7d0RBQzlDekUsV0FBVyw2RUFJVixPQUhDNkcscUJBQXFCMEUsU0FBUzlHLEVBQUUsR0FDNUIsK0JBQ0E7a0VBR04sNEVBQUN4RTs0REFBSUQsV0FBVTs7OEVBQ2IsOERBQUNDO29FQUFJRCxXQUFVOztzRkFDYiw4REFBQzJOOzRFQUFLM04sV0FBVTtzRkFBcUN1TCxTQUFTOUcsRUFBRTs7Ozs7O3NGQUNoRSw4REFBQ3hFOzRFQUFJRCxXQUFVO3NGQUNiLDRFQUFDQztnRkFBSUQsV0FBVTswRkFBMkR1TCxTQUFTNUcsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0VBRzFGa0MscUJBQXFCMEUsU0FBUzlHLEVBQUUsa0JBQy9CLDhEQUFDbEYsaU9BQVNBO29FQUFDUyxXQUFVOzs7Ozs7Ozs7Ozs7dURBaEJwQnVMLFNBQVM5RyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBMEIxQiw4REFBQy9GLDBEQUFJQTtvQ0FBQ3NCLFdBQVU7OENBQ2QsNEVBQUNyQiw4REFBUUE7d0NBQUNxQixXQUFVOzswREFDbEIsOERBQUN4QixnRUFBVUE7Z0RBQUMyTyxTQUFRO2dEQUFLbk4sV0FBVTswREFBMEQ7Ozs7OzswREFHN0YsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFFYiw4REFBQ0M7d0RBQ0NtTixTQUFTLElBQU03RixvQkFBb0I7d0RBQ25DdkgsV0FBVywrRUFJVixPQUhDc0gscUJBQXFCLFNBQ2pCLHlDQUNBOzs0REFHTEEscUJBQXFCLHdCQUNwQiw4REFBQ3JIO2dFQUFJRCxXQUFVOzBFQUNiLDRFQUFDQztvRUFBSUQsV0FBVTs4RUFDYiw0RUFBQ0U7d0VBQUlGLFdBQVU7d0VBQXFCSSxNQUFLO3dFQUFlRCxTQUFRO2tGQUM5RCw0RUFBQ0U7NEVBQUsyTixVQUFTOzRFQUFVMU4sR0FBRTs0RUFBcUgyTixVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBTWpLLDhEQUFDaE87Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDQzt3RUFBSUQsV0FBVTtrRkFBNkY7Ozs7OztrRkFHNUcsOERBQUNDO3dFQUFJRCxXQUFVO2tGQUF1Qzs7Ozs7O2tGQUN0RCw4REFBQ0M7d0VBQUlELFdBQVU7a0ZBQXVDOzs7Ozs7a0ZBQ3RELDhEQUFDQzt3RUFBSUQsV0FBVTtrRkFBaUM7Ozs7OztrRkFDaEQsOERBQUNDO3dFQUFJRCxXQUFVO2tGQUE2Qjs7Ozs7O2tGQUU1Qyw4REFBQ0M7d0VBQUlELFdBQVU7OzBGQUNiLDhEQUFDQztnRkFBSUQsV0FBVTs7a0dBQ2IsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQWdCOzs7Ozs7a0dBQ2hDLDhEQUFDMk47d0ZBQUszTixXQUFVO2tHQUE4Qjs7Ozs7Ozs7Ozs7OzBGQUVoRCw4REFBQ0M7Z0ZBQUlELFdBQVU7O2tHQUNiLDhEQUFDMk47d0ZBQUszTixXQUFVO2tHQUFnQjs7Ozs7O2tHQUNoQyw4REFBQzJOO3dGQUFLM04sV0FBVTtrR0FBOEI7Ozs7Ozs7Ozs7OzswRkFFaEQsOERBQUNDO2dGQUFJRCxXQUFVOztrR0FDYiw4REFBQzJOO3dGQUFLM04sV0FBVTtrR0FBZ0I7Ozs7OztrR0FDaEMsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQThCOzs7Ozs7Ozs7Ozs7MEZBRWhELDhEQUFDQztnRkFBSUQsV0FBVTs7a0dBQ2IsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQWdCOzs7Ozs7a0dBQ2hDLDhEQUFDMk47d0ZBQUszTixXQUFVO2tHQUE4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU90RCw4REFBQ0M7d0RBQ0NtTixTQUFTLElBQU03RixvQkFBb0I7d0RBQ25DdkgsV0FBVywrRUFJVixPQUhDc0gscUJBQXFCLFNBQ2pCLHlDQUNBOzs0REFHTEEscUJBQXFCLHdCQUNwQiw4REFBQ3JIO2dFQUFJRCxXQUFVOzBFQUNiLDRFQUFDQztvRUFBSUQsV0FBVTs4RUFDYiw0RUFBQ0U7d0VBQUlGLFdBQVU7d0VBQXFCSSxNQUFLO3dFQUFlRCxTQUFRO2tGQUM5RCw0RUFBQ0U7NEVBQUsyTixVQUFTOzRFQUFVMU4sR0FBRTs0RUFBcUgyTixVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBTWpLLDhEQUFDaE87Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDQzt3RUFBSUQsV0FBVTtrRkFBNEM7Ozs7OztrRkFDM0QsOERBQUNDO3dFQUFJRCxXQUFVO2tGQUF3Qzs7Ozs7O2tGQUV2RCw4REFBQ0M7d0VBQUlELFdBQVU7OzBGQUNiLDhEQUFDQztnRkFBSUQsV0FBVTs7a0dBQ2IsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQWdCOzs7Ozs7a0dBQ2hDLDhEQUFDMk47d0ZBQUszTixXQUFVO2tHQUE4Qjs7Ozs7Ozs7Ozs7OzBGQUVoRCw4REFBQ0M7Z0ZBQUlELFdBQVU7O2tHQUNiLDhEQUFDMk47d0ZBQUszTixXQUFVO2tHQUFnQjs7Ozs7O2tHQUNoQyw4REFBQzJOO3dGQUFLM04sV0FBVTtrR0FBOEI7Ozs7Ozs7Ozs7OzswRkFFaEQsOERBQUNDO2dGQUFJRCxXQUFVOztrR0FDYiw4REFBQzJOO3dGQUFLM04sV0FBVTtrR0FBZ0I7Ozs7OztrR0FDaEMsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQThCOzs7Ozs7Ozs7Ozs7MEZBRWhELDhEQUFDQztnRkFBSUQsV0FBVTs7a0dBQ2IsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQWdCOzs7Ozs7a0dBQ2hDLDhEQUFDMk47d0ZBQUszTixXQUFVO2tHQUE4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBVTVELDhEQUFDdEIsMERBQUlBO29DQUFDc0IsV0FBVTs4Q0FDZCw0RUFBQ3JCLDhEQUFRQTt3Q0FBQ3FCLFdBQVU7OzBEQUNsQiw4REFBQ3hCLGdFQUFVQTtnREFBQzJPLFNBQVE7Z0RBQUtuTixXQUFVOzBEQUEwRDs7Ozs7OzBEQUk3Riw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUViLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUMyTjtnRUFBSzNOLFdBQVU7MEVBQWlEOzs7Ozs7MEVBQ2pFLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNrTzt3RUFDQ25MLE9BQU95RTt3RUFDUDJHLFVBQVUsQ0FBQ3JFLElBQU1yQyxxQkFBcUJxQyxFQUFFQyxNQUFNLENBQUNoSCxLQUFLO3dFQUNwRC9DLFdBQVU7OzBGQUVWLDhEQUFDb087Z0ZBQU9yTCxPQUFNOzBGQUFPOzs7Ozs7MEZBQ3JCLDhEQUFDcUw7Z0ZBQU9yTCxPQUFNOzBGQUFVOzs7Ozs7Ozs7Ozs7a0ZBRTFCLDhEQUFDOUM7d0VBQUlELFdBQVU7a0ZBQ1p3SCxzQkFBc0IsMEJBQ3JCLDhEQUFDbUc7NEVBQUszTixXQUFVO3NGQUErQzs7Ozs7aUdBRS9ELDhEQUFDMk47NEVBQUszTixXQUFVO3NGQUFnRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBT3hFLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUMyTjtnRUFBSzNOLFdBQVU7MEVBQWlEOzs7Ozs7MEVBQ2pFLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNDO3dFQUFJRCxXQUFVO2tGQUFpRDs7Ozs7O2tGQUNoRSw4REFBQ0M7d0VBQUlELFdBQVU7a0ZBQXFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS3hELDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUMyTjtnRUFBSzNOLFdBQVU7MEVBQWlEOzs7Ozs7MEVBQ2pFLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNrTzt3RUFDQ25MLE9BQU8yRTt3RUFDUHlHLFVBQVUsQ0FBQ3JFLElBQU1uQyxpQkFBaUJ0RSxTQUFTeUcsRUFBRUMsTUFBTSxDQUFDaEgsS0FBSzt3RUFDekQvQyxXQUFVOzswRkFFViw4REFBQ29PO2dGQUFPckwsT0FBTzswRkFBRzs7Ozs7OzBGQUNsQiw4REFBQ3FMO2dGQUFPckwsT0FBTzswRkFBRzs7Ozs7Ozs7Ozs7O2tGQUVwQiw4REFBQzlDO3dFQUFJRCxXQUFVO2tGQUNaMEgsa0JBQWtCLGtCQUNqQiw4REFBQ2lHOzRFQUFLM04sV0FBVTtzRkFBK0M7Ozs7O2lHQUUvRCw4REFBQzJOOzRFQUFLM04sV0FBVTtzRkFBZ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBVTlFLDhEQUFDdEIsMERBQUlBO29DQUFDc0IsV0FBVTs4Q0FDZCw0RUFBQ3JCLDhEQUFRQTt3Q0FBQ3FCLFdBQVU7OzBEQUNsQiw4REFBQ3hCLGdFQUFVQTtnREFBQzJPLFNBQVE7Z0RBQUtuTixXQUFVOzBEQUEwRDs7Ozs7OzBEQUk3Riw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUViLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUMyTjtnRUFBSzNOLFdBQVU7MEVBQWlEOzs7Ozs7MEVBQ2pFLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNrTzt3RUFDQ25MLE9BQU82RTt3RUFDUHVHLFVBQVUsQ0FBQ3JFLElBQU1qQyxpQkFBaUJpQyxFQUFFQyxNQUFNLENBQUNoSCxLQUFLO3dFQUNoRC9DLFdBQVU7OzBGQUVWLDhEQUFDb087Z0ZBQU9yTCxPQUFNOzBGQUFPOzs7Ozs7MEZBQ3JCLDhEQUFDcUw7Z0ZBQU9yTCxPQUFNOzBGQUFROzs7Ozs7MEZBQ3RCLDhEQUFDcUw7Z0ZBQU9yTCxPQUFNOzBGQUFROzs7Ozs7MEZBQ3RCLDhEQUFDcUw7Z0ZBQU9yTCxPQUFNOzBGQUFROzs7Ozs7MEZBQ3RCLDhEQUFDcUw7Z0ZBQU9yTCxPQUFNOzBGQUFNOzs7Ozs7Ozs7Ozs7a0ZBRXRCLDhEQUFDOUM7d0VBQUlELFdBQVU7OzRFQUNaNEgsa0JBQWtCLHlCQUFXLDhEQUFDK0Y7Z0ZBQUszTixXQUFVOzBGQUErQzs7Ozs7OzRFQUM1RjRILGtCQUFrQix5QkFBVyw4REFBQytGO2dGQUFLM04sV0FBVTswRkFBK0M7Ozs7Ozs0RUFDNUY0SCxrQkFBa0IseUJBQVcsOERBQUMrRjtnRkFBSzNOLFdBQVU7MEZBQStDOzs7Ozs7NEVBQzVGNEgsa0JBQWtCLHVCQUFTLDhEQUFDK0Y7Z0ZBQUszTixXQUFVOzBGQUErQzs7Ozs7OzRFQUMxRjRILGtCQUFrQix3QkFBVSw4REFBQytGO2dGQUFLM04sV0FBVTswRkFBZ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFNbkcsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQzJOO2dFQUFLM04sV0FBVTswRUFBaUQ7Ozs7OzswRUFDakUsOERBQUNDO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ2tPO3dFQUNDbkwsT0FBTytFO3dFQUNQcUcsVUFBVSxDQUFDckUsSUFBTS9CLG9CQUFvQitCLEVBQUVDLE1BQU0sQ0FBQ2hILEtBQUs7d0VBQ25EL0MsV0FBVTs7MEZBRVYsOERBQUNvTztnRkFBT3JMLE9BQU07MEZBQVk7Ozs7OzswRkFDMUIsOERBQUNxTDtnRkFBT3JMLE9BQU07MEZBQVU7Ozs7Ozs7Ozs7OztrRkFFMUIsOERBQUM5Qzt3RUFBSUQsV0FBVTtrRkFDWjhILHFCQUFxQiw0QkFDcEIsOERBQUM2Rjs0RUFBSzNOLFdBQVU7c0ZBQWdEOzs7OztpR0FFaEUsOERBQUMyTjs0RUFBSzNOLFdBQVU7c0ZBQStDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFPdkUsOERBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQzJOO2dFQUFLM04sV0FBVTswRUFBaUQ7Ozs7OzswRUFDakUsOERBQUNDO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ2tPO3dFQUNDbkwsT0FBT2lGO3dFQUNQbUcsVUFBVSxDQUFDckUsSUFBTTdCLGNBQWM2QixFQUFFQyxNQUFNLENBQUNoSCxLQUFLO3dFQUM3Qy9DLFdBQVU7OzBGQUVWLDhEQUFDb087Z0ZBQU9yTCxPQUFNOzBGQUFPOzs7Ozs7MEZBQ3JCLDhEQUFDcUw7Z0ZBQU9yTCxPQUFNOzBGQUFPOzs7Ozs7Ozs7Ozs7a0ZBRXZCLDhEQUFDOUM7d0VBQUlELFdBQVU7a0ZBQ1pnSSxlQUFlLHVCQUNkLDhEQUFDMkY7NEVBQUszTixXQUFVO3NGQUErQzs7Ozs7aUdBRS9ELDhEQUFDMk47NEVBQUszTixXQUFVO3NGQUFnRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBT3hFLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUMyTjtnRUFBSzNOLFdBQVU7MEVBQWlEOzs7Ozs7MEVBQ2pFLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNrTzt3RUFDQ25MLE9BQU9tRjt3RUFDUGlHLFVBQVUsQ0FBQ3JFLElBQU0zQixPQUFPMkIsRUFBRUMsTUFBTSxDQUFDaEgsS0FBSzt3RUFDdEMvQyxXQUFVOzswRkFFViw4REFBQ29PO2dGQUFPckwsT0FBTTswRkFBTzs7Ozs7OzBGQUNyQiw4REFBQ3FMO2dGQUFPckwsT0FBTTswRkFBUTs7Ozs7OzBGQUN0Qiw4REFBQ3FMO2dGQUFPckwsT0FBTTswRkFBVzs7Ozs7Ozs7Ozs7O2tGQUUzQiw4REFBQzlDO3dFQUFJRCxXQUFVOzs0RUFDWmtJLFFBQVEseUJBQ1AsOERBQUNqSTtnRkFBSUQsV0FBVTs7a0dBQ2IsOERBQUNDO3dGQUFJRCxXQUFVO2tHQUErQzs7Ozs7O2tHQUM5RCw4REFBQ0M7d0ZBQUlELFdBQVU7a0dBQW1DOzs7Ozs7Ozs7Ozs7NEVBR3JEa0ksUUFBUSw0QkFDUCw4REFBQ2pJO2dGQUFJRCxXQUFVOztrR0FDYiw4REFBQ0M7d0ZBQUlELFdBQVU7a0dBQStDOzs7Ozs7a0dBQzlELDhEQUFDQzt3RkFBSUQsV0FBVTtrR0FBbUM7Ozs7Ozs7Ozs7Ozs0RUFHckRrSSxRQUFRLHdCQUFVLDhEQUFDeUY7Z0ZBQUszTixXQUFVOzBGQUFnRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUy9GLDhEQUFDdEIsMERBQUlBO29DQUFDc0IsV0FBVTs4Q0FDZCw0RUFBQ3JCLDhEQUFRQTt3Q0FBQ3FCLFdBQVU7OzBEQUNsQiw4REFBQ3hCLGdFQUFVQTtnREFBQzJPLFNBQVE7Z0RBQUtuTixXQUFVOzBEQUFzRDs7Ozs7OzBEQUl6Riw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUViLDhEQUFDQzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUM0TjtnRUFBTTVOLFdBQVU7MEVBQStEOzs7Ozs7MEVBR2hGLDhEQUFDMk47Z0VBQUszTixXQUFVOzBFQUFvRDs7Ozs7Ozs7Ozs7O2tFQU10RSw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNiLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUM0Tjt3RUFBTTVOLFdBQVU7a0ZBQXVFOzs7Ozs7a0ZBR3hGLDhEQUFDQzt3RUFBSUQsV0FBVTs7MEZBQ2IsOERBQUMrTjtnRkFDQ3RJLE1BQUs7Z0ZBQ0wySCxTQUFTLEtBQXdDO2dGQUNqRHBOLFdBQVU7MEZBQ1g7Ozs7OzswRkFHRCw4REFBQ0M7Z0ZBQUlELFdBQVU7O2tHQUNiLDhEQUFDcU87d0ZBQ0M1SSxNQUFNK0MsZUFBZSxTQUFTO3dGQUM5QnpGLE9BQU91Rjt3RkFDUDZGLFVBQVV0RTt3RkFDVnlFLGFBQVk7d0ZBQ1p0TyxXQUFXLDhEQUVWLE9BREMwSSxnQkFBZ0IsNkJBQTZCOzs7Ozs7a0dBR2pELDhEQUFDcUY7d0ZBQ0N0SSxNQUFLO3dGQUNMMkgsU0FBUyxJQUFNM0UsZ0JBQWdCLENBQUNEO3dGQUNoQ3hJLFdBQVU7a0dBRVR3SSw2QkFDQyw4REFBQzVJLGlPQUFVQTs0RkFBQ0ksV0FBVTs7Ozs7aUhBRXRCLDhEQUFDTCxpT0FBT0E7NEZBQUNLLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzREQU81QjBJLCtCQUNDLDhEQUFDekk7Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDMk47d0VBQUszTixXQUFVO2tGQUFnRzs7Ozs7O29FQUFROzs7Ozs7OzBFQUs1SCw4REFBQ0M7Z0VBQUlELFdBQVU7MEVBQ2IsNEVBQUNrRjs4RUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FjZiw4REFBQ2pGOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDQztnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ3RCLDBEQUFJQTtvQ0FBQ3NCLFdBQVU7OENBQ2hCLDRFQUFDckIsOERBQVFBO3dDQUFDcUIsV0FBVTs7MERBQ2xCLDhEQUFDeEIsZ0VBQVVBO2dEQUFDMk8sU0FBUTtnREFBS25OLFdBQVU7MERBQ2hDZSxFQUFFOzs7Ozs7MERBR0wsOERBQUNkO2dEQUFJRCxXQUFVOztrRUFFZiw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNiLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNDO3dFQUFJRCxXQUFVOzswRkFDYiw4REFBQzJOO2dGQUFLM04sV0FBVTswRkFBc0NnQyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWMyQyxJQUFJLEtBQUk7Ozs7OzswRkFDNUUsOERBQUMxRTtnRkFBSUQsV0FBVTs7a0dBQ2IsOERBQUMyTjt3RkFBSzNOLFdBQVU7a0dBQXdCOzs7Ozs7a0dBQ3hDLDhEQUFDQzt3RkFBSUQsV0FBVTs7MEdBQ2IsOERBQUN2Qiw0REFBTUE7Z0dBQ0wrTyxNQUFLO2dHQUNMTCxTQUFRO2dHQUNSQyxTQUFTLElBQU0vRixZQUFZaUMsS0FBS2lGLEdBQUcsQ0FBQyxHQUFHbkgsV0FBVztnR0FDbERvSCxVQUFVcEgsYUFBYTtnR0FDdkJwSCxXQUFVOzBHQUNYOzs7Ozs7MEdBR0QsOERBQUMyTjtnR0FBSzNOLFdBQVU7MEdBQXVDb0g7Ozs7OzswR0FDdkQsOERBQUMzSSw0REFBTUE7Z0dBQ0wrTyxNQUFLO2dHQUNMTCxTQUFRO2dHQUNSQyxTQUFTLElBQU0vRixZQUFZaUMsS0FBS21GLEdBQUcsQ0FBQyxJQUFJckgsV0FBVztnR0FDbkRvSCxVQUFVcEgsYUFBYTtnR0FDdkJwSCxXQUFVOzBHQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBTVAsOERBQUMyTjt3RUFBSzNOLFdBQVU7OzRFQUFtRGdDLENBQUFBLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBYzRDLEtBQUssS0FBSSxLQUFLd0M7NEVBQVM7Ozs7Ozs7Ozs7Ozs7NERBR3pHSixnQkFBZ0IsbUJBQ2YsOERBQUMvRztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUMyTjt3RUFBSzNOLFdBQVU7OzRFQUF5RDs0RUFBcUJnSDs0RUFBYzs0RUFBSUk7NEVBQVM7Ozs7Ozs7a0ZBQ3pILDhEQUFDdUc7d0VBQUszTixXQUFVOzs0RUFBa0RnSCxnQkFBZ0IsS0FBS0k7NEVBQVM7Ozs7Ozs7Ozs7Ozs7NERBSW5HLENBQUNsRixnQkFBZ0JnRiwrQkFDaEIsOERBQUNqSDtnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUMyTjt3RUFBSzNOLFdBQVU7OzRFQUF5RDs0RUFBMEJvSDs7Ozs7OztrRkFDbkcsOERBQUN1Rzt3RUFBSzNOLFdBQVU7OzRFQUFrRCxLQUFLb0g7NEVBQVM7Ozs7Ozs7Ozs7Ozs7NERBSW5GTixtQkFBbUIsMkJBQ2xCLDhEQUFDN0c7Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDMk47d0VBQUszTixXQUFVOzs0RUFBMkM7NEVBQVk4RyxtQkFBbUIsWUFBWSxPQUFPOzRFQUFNOzs7Ozs7O2tGQUNuSCw4REFBQzZHO3dFQUFLM04sV0FBVTs7NEVBQXFDOzRFQUFFc0osS0FBS3dFLEtBQUssQ0FBQyxDQUFDOUwsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjNEMsS0FBSyxLQUFJLEtBQUt3QyxXQUFZTixDQUFBQSxtQkFBbUIsWUFBWSxJQUFJLE9BQU8sS0FBSyxJQUFHOzRFQUFJOzs7Ozs7Ozs7Ozs7OzBFQUlySyw4REFBQzRIO2dFQUFHMU8sV0FBVTs7Ozs7OzBFQUVkLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUMyTjt3RUFBSzNOLFdBQVU7a0ZBQXVCZSxFQUFFOzs7Ozs7a0ZBQ3pDLDhEQUFDNE07d0VBQUszTixXQUFVOzs0RUFBMENzSixLQUFLd0UsS0FBSyxDQUFDbEI7NEVBQWtCOzRFQUFNOUYsbUJBQW1CLFlBQVksU0FBU0EsbUJBQW1CLFlBQVksV0FBVzs7Ozs7Ozs7Ozs7OzswRUFJakwsOERBQUM3RztnRUFBSUQsV0FBVTswRUFDYiw0RUFBQ0M7b0VBQUlELFdBQVU7O3NGQUNiLDhEQUFDQzs0RUFBSUQsV0FBVTs7OEZBQWlCLDhEQUFDMk87O3dGQUFRNU4sRUFBRTt3RkFBWTs7Ozs7OztnRkFBVTs4RkFBTSw4REFBQzRNO29GQUFLM04sV0FBVTsrRkFBYVcseUJBQUFBLGlCQUFpQm9FLElBQUksQ0FBQ2lILENBQUFBLEtBQU1BLEdBQUd2SCxFQUFFLEtBQUttQyx5QkFBdENqRyw2Q0FBQUEsdUJBQW1EZ0UsSUFBSTs7Ozs7Ozs7Ozs7O3NGQUMzSiw4REFBQzFFOzRFQUFJRCxXQUFVOzs4RkFBaUIsOERBQUMyTzs7d0ZBQVE1TixFQUFFO3dGQUFrQjs7Ozs7OztnRkFBVTs4RkFBTSw4REFBQzRNO29GQUFLM04sV0FBVTsrRkFBYVksa0JBQUFBLFVBQVVtRSxJQUFJLENBQUM2SixDQUFBQSxNQUFPQSxJQUFJbkssRUFBRSxLQUFLb0MsK0JBQWpDakcsc0NBQUFBLGdCQUFvRCtELElBQUk7Ozs7Ozs7Ozs7OztzRkFDbEssOERBQUMxRTs0RUFBSUQsV0FBVTs7OEZBQWlCLDhEQUFDMk87OEZBQU87Ozs7OztnRkFBaUI7OEZBQU0sOERBQUNoQjtvRkFBSzNOLFdBQVU7OEZBQWE4RyxtQkFBbUIsWUFBWSxXQUFXQSxtQkFBbUIsWUFBWSxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLdEwsOERBQUNySSw0REFBTUE7d0RBQ0wrTyxNQUFLO3dEQUNMeE4sV0FBVTt3REFDVm9OLFNBQVNwRDt3REFDVHdFLFVBQVU3TSxnQkFBZ0JKLFdBQVcsQ0FBQ1M7a0VBRXJDTCxlQUFlLHNCQUFzQjs7Ozs7O2tFQUd4Qyw4REFBQzFCO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDWCxpT0FBVUE7d0VBQUNXLFdBQVU7Ozs7OztrRkFDdEIsOERBQUMyTjtrRkFBSzs7Ozs7Ozs7Ozs7OzBFQUVSLDhEQUFDMU47Z0VBQUlELFdBQVU7O2tGQUNiLDhEQUFDVixpT0FBU0E7d0VBQUNVLFdBQVU7Ozs7OztrRkFDckIsOERBQUMyTjtrRkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFhMUI7R0FsM0N3QmpOOztRQUNEckMsNERBQWVBO1FBQ3JCQyxzREFBU0E7UUFDZEMsc0RBQWVBO1FBQ0FRLDZEQUFPQTs7O01BSlYyQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL1tsb2NhbGVdLyhzaGFyZWRQYWdlcykvaG9zdGluZy92cHMvY29uZmlndXJlL3BhZ2UuanN4P2YyMjciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zLCB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gXCJuZXh0LWludGxcIjtcclxuaW1wb3J0IHsgVHlwb2dyYXBoeSwgQnV0dG9uLCBDYXJkLCBDYXJkQm9keSB9IGZyb20gXCJAbWF0ZXJpYWwtdGFpbHdpbmQvcmVhY3RcIjtcclxuaW1wb3J0IHZwc1NlcnZpY2UgZnJvbSBcIkAvYXBwL3NlcnZpY2VzL3Zwc1NlcnZpY2VcIjtcclxuaW1wb3J0IGNhcnRTZXJ2aWNlIGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9jYXJ0U2VydmljZVwiO1xyXG5pbXBvcnQgcGFja2FnZVNlcnZpY2UgZnJvbSBcIkAvYXBwL3NlcnZpY2VzL3BhY2thZ2VTZXJ2aWNlXCI7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tIFwiQC9hcHAvY29udGV4dC9BdXRoQ29udGV4dFwiO1xyXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJyZWFjdC10b2FzdGlmeVwiO1xyXG5pbXBvcnQge1xyXG4gIFNlcnZlckljb24sXHJcbiAgQ3B1SWNvbixcclxuICBIYXJkRHJpdmVJY29uLFxyXG4gIEdsb2JlSWNvbixcclxuICBTaGllbGRJY29uLFxyXG4gIENsb2NrSWNvbixcclxuICBDaGVja0ljb24sXHJcbiAgQXJyb3dMZWZ0SWNvbixcclxuICBNb25pdG9ySWNvbixcclxuICBUZXJtaW5hbEljb24sXHJcbiAgRXllSWNvbixcclxuICBFeWVPZmZJY29uLFxyXG4gIEtleUljb24sXHJcbiAgVXNlckljb25cclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcblxyXG4vLyBNb2Rlcm4gT1MgSWNvbnMgQ29tcG9uZW50c1xyXG5jb25zdCBVYnVudHVJY29uID0gKHsgY2xhc3NOYW1lIH0pID0+IChcclxuICA8ZGl2IGNsYXNzTmFtZT17YCR7Y2xhc3NOYW1lfSBiZy1ncmFkaWVudC10by1iciBmcm9tLW9yYW5nZS01MDAgdG8tb3JhbmdlLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyYH0+XHJcbiAgICA8c3ZnIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBjbGFzc05hbWU9XCJ3LTMvNCBoLTMvNCB0ZXh0LXdoaXRlXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxyXG4gICAgICA8cGF0aCBkPVwiTTEyIDBDNS4zNzMgMCAwIDUuMzczIDAgMTJzNS4zNzMgMTIgMTIgMTIgMTItNS4zNzMgMTItMTJTMTguNjI3IDAgMTIgMHptMy4yNzkgMTguMjc5Yy0uNTUzLjU1My0xLjQ0Ny41NTMtMiAwcy0uNTUzLTEuNDQ3IDAtMiAxLjQ0Ny0uNTUzIDIgMCAuNTUzIDEuNDQ3IDAgMnptLTYuNTU4IDBjLS41NTMuNTUzLTEuNDQ3LjU1My0yIDBzLS41NTMtMS40NDcgMC0yIDEuNDQ3LS41NTMgMiAwIC41NTMgMS40NDcgMCAyem0zLjI3OS02LjU1OGMtLjU1My41NTMtMS40NDcuNTUzLTIgMHMtLjU1My0xLjQ0NyAwLTIgMS40NDctLjU1MyAyIDAgLjU1MyAxLjQ0NyAwIDJ6XCIvPlxyXG4gICAgPC9zdmc+XHJcbiAgPC9kaXY+XHJcbik7XHJcblxyXG5jb25zdCBDZW50T1NJY29uID0gKHsgY2xhc3NOYW1lIH0pID0+IChcclxuICA8ZGl2IGNsYXNzTmFtZT17YCR7Y2xhc3NOYW1lfSBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS02MDAgdG8tcHVycGxlLTcwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyYH0+XHJcbiAgICA8c3ZnIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBjbGFzc05hbWU9XCJ3LTMvNCBoLTMvNCB0ZXh0LXdoaXRlXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxyXG4gICAgICA8cGF0aCBkPVwiTTEyIDJMMiA3djEwbDEwIDUgMTAtNVY3bC0xMC01em0wIDIuNUwxOS41IDguNXY3TDEyIDE5LjVsLTcuNS00di03TDEyIDQuNXpcIi8+XHJcbiAgICA8L3N2Zz5cclxuICA8L2Rpdj5cclxuKTtcclxuXHJcbmNvbnN0IERlYmlhbkljb24gPSAoeyBjbGFzc05hbWUgfSkgPT4gKFxyXG4gIDxkaXYgY2xhc3NOYW1lPXtgJHtjbGFzc05hbWV9IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcmVkLTYwMCB0by1yZWQtNzAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJgfT5cclxuICAgIDxzdmcgdmlld0JveD1cIjAgMCAyNCAyNFwiIGNsYXNzTmFtZT1cInctMy80IGgtMy80IHRleHQtd2hpdGVcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgIDxwYXRoIGQ9XCJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnptLTEgMTcuOTNjLTMuOTQtLjQ5LTctMy44NS03LTcuOTMgMC0uNjIuMDgtMS4yMS4yMS0xLjc5TDkgMTV2MWMwIDEuMS45IDIgMiAydjEuOTN6bTYuOS0yLjU0Yy0uMjYtLjgxLTEtMS4zOS0xLjktMS4zOWgtMXYtM2MwLS41NS0uNDUtMS0xLTFIOHYtMmgyYy41NSAwIDEtLjQ1IDEtMVY3aDJjMS4xIDAgMi0uOSAyLTJ2LS40MWMyLjkzIDEuMTkgNSA0LjA2IDUgNy40MSAwIDIuMDgtLjggMy45Ny0yLjEgNS4zOXpcIi8+XHJcbiAgICA8L3N2Zz5cclxuICA8L2Rpdj5cclxuKTtcclxuXHJcbmNvbnN0IFdpbmRvd3NJY29uID0gKHsgY2xhc3NOYW1lIH0pID0+IChcclxuICA8ZGl2IGNsYXNzTmFtZT17YCR7Y2xhc3NOYW1lfSBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNjAwIHRvLWJsdWUtNzAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJgfT5cclxuICAgIDxzdmcgdmlld0JveD1cIjAgMCAyNCAyNFwiIGNsYXNzTmFtZT1cInctMy80IGgtMy80IHRleHQtd2hpdGVcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgIDxwYXRoIGQ9XCJNMyAxMlY2Ljc1bDYtMS4zMnY2LjQ4TDMgMTJ6bTE3LTl2OC43NWwtMTAgLjE1VjUuMjFMMjAgM3pNMyAxM2w2IC4wOXY2LjgxbC02LTEuMTVWMTN6bTE3IC4yNVYyMmwtMTAtMS45MVYxMy4xbDEwIC4xNXpcIi8+XHJcbiAgICA8L3N2Zz5cclxuICA8L2Rpdj5cclxuKTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvbmZpZ3VyZVZQU1BhZ2UoKSB7XHJcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCk7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucyhcInZwc19jb25maWd1cmVcIik7XHJcbiAgY29uc3QgeyBzZXRDYXJ0Q291bnQgfSA9IHVzZUF1dGgoKTtcclxuXHJcbiAgLy8gU3RhdGUgbWFuYWdlbWVudFxyXG4gIGNvbnN0IFt2cHNQbGFucywgc2V0VnBzUGxhbnNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtvc0ltYWdlcywgc2V0T3NJbWFnZXNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtyZWdpb25zLCBzZXRSZWdpb25zXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtvcmRlckxvYWRpbmcsIHNldE9yZGVyTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIEluaXRpYWxpemUgcGxhbiBmcm9tIFVSTCBwYXJhbXNcclxuICBjb25zdCBwbGFuSWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdwbGFuJyk7XHJcbiAgY29uc3QgYXV0b0JhY2t1cCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2F1dG9iYWNrdXAnKSA9PT0gJ3RydWUnO1xyXG5cclxuICBjb25zdCBbc2VsZWN0ZWRQbGFuLCBzZXRTZWxlY3RlZFBsYW5dID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2lzQXV0b0JhY2t1cCwgc2V0SXNBdXRvQmFja3VwXSA9IHVzZVN0YXRlKGF1dG9CYWNrdXApO1xyXG5cclxuICAvLyBGdW5jdGlvbiB0byBwYXJzZSBzcGVjaWZpY2F0aW9ucyBmcm9tIGRhdGFiYXNlXHJcbiAgY29uc3QgcGFyc2VTcGVjaWZpY2F0aW9ucyA9IChzcGVjaWZpY2F0aW9ucywgZGVzY3JpcHRpb24pID0+IHtcclxuICAgIGxldCBjb3JlcyA9IDAsIHJhbSA9IFwiMCBHQlwiLCBzdG9yYWdlID0gXCIwIEdCXCIsIHRyYWZmaWMgPSBcIjMyIFRCXCI7XHJcblxyXG4gICAgLy8gUGFyc2UgZnJvbSBzcGVjaWZpY2F0aW9ucyBhcnJheVxyXG4gICAgaWYgKHNwZWNpZmljYXRpb25zICYmIEFycmF5LmlzQXJyYXkoc3BlY2lmaWNhdGlvbnMpKSB7XHJcbiAgICAgIHNwZWNpZmljYXRpb25zLmZvckVhY2goc3BlYyA9PiB7XHJcbiAgICAgICAgY29uc3QgdmFsdWUgPSBzcGVjLnZhbHVlIHx8IFwiXCI7XHJcbiAgICAgICAgY29uc3QgbG93ZXJWYWx1ZSA9IHZhbHVlLnRvTG93ZXJDYXNlKCk7XHJcblxyXG4gICAgICAgIC8vIFBhcnNlIENQVSBjb3Jlc1xyXG4gICAgICAgIGlmIChsb3dlclZhbHVlLmluY2x1ZGVzKFwiY3B1XCIpIHx8IGxvd2VyVmFsdWUuaW5jbHVkZXMoXCJjb3JlXCIpIHx8IGxvd2VyVmFsdWUuaW5jbHVkZXMoXCJ2Y3B1XCIpKSB7XHJcbiAgICAgICAgICBjb25zdCBjcHVNYXRjaCA9IHZhbHVlLm1hdGNoKC8oXFxkKykvKTtcclxuICAgICAgICAgIGlmIChjcHVNYXRjaCkgY29yZXMgPSBwYXJzZUludChjcHVNYXRjaFsxXSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBQYXJzZSBSQU1cclxuICAgICAgICBpZiAobG93ZXJWYWx1ZS5pbmNsdWRlcyhcInJhbVwiKSB8fCBsb3dlclZhbHVlLmluY2x1ZGVzKFwibWVtb3J5XCIpIHx8IGxvd2VyVmFsdWUuaW5jbHVkZXMoXCJnYiByYW1cIikpIHtcclxuICAgICAgICAgIGNvbnN0IHJhbU1hdGNoID0gdmFsdWUubWF0Y2goLyhcXGQrKVxccypnYi9pKTtcclxuICAgICAgICAgIGlmIChyYW1NYXRjaCkgcmFtID0gYCR7cmFtTWF0Y2hbMV19IEdCYDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFBhcnNlIFN0b3JhZ2VcclxuICAgICAgICBpZiAobG93ZXJWYWx1ZS5pbmNsdWRlcyhcInN0b3JhZ2VcIikgfHwgbG93ZXJWYWx1ZS5pbmNsdWRlcyhcImRpc2tcIikgfHwgbG93ZXJWYWx1ZS5pbmNsdWRlcyhcInNzZFwiKSB8fCBsb3dlclZhbHVlLmluY2x1ZGVzKFwibnZtZVwiKSkge1xyXG4gICAgICAgICAgY29uc3Qgc3RvcmFnZU1hdGNoID0gdmFsdWUubWF0Y2goLyhcXGQrKVxccypnYi9pKTtcclxuICAgICAgICAgIGlmIChzdG9yYWdlTWF0Y2gpIHtcclxuICAgICAgICAgICAgY29uc3Qgc3RvcmFnZVR5cGUgPSBsb3dlclZhbHVlLmluY2x1ZGVzKFwibnZtZVwiKSA/IFwiTlZNZVwiIDogbG93ZXJWYWx1ZS5pbmNsdWRlcyhcInNzZFwiKSA/IFwiU1NEXCIgOiBcIlwiO1xyXG4gICAgICAgICAgICBzdG9yYWdlID0gYCR7c3RvcmFnZU1hdGNoWzFdfSBHQiAke3N0b3JhZ2VUeXBlfWAudHJpbSgpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUGFyc2UgVHJhZmZpYy9CYW5kd2lkdGhcclxuICAgICAgICBpZiAobG93ZXJWYWx1ZS5pbmNsdWRlcyhcInRyYWZmaWNcIikgfHwgbG93ZXJWYWx1ZS5pbmNsdWRlcyhcImJhbmR3aWR0aFwiKSB8fCBsb3dlclZhbHVlLmluY2x1ZGVzKFwidHJhbnNmZXJcIikpIHtcclxuICAgICAgICAgIGNvbnN0IHRyYWZmaWNNYXRjaCA9IHZhbHVlLm1hdGNoKC8oXFxkKylcXHMqKHRifGdiKS9pKTtcclxuICAgICAgICAgIGlmICh0cmFmZmljTWF0Y2gpIHtcclxuICAgICAgICAgICAgdHJhZmZpYyA9IGAke3RyYWZmaWNNYXRjaFsxXX0gJHt0cmFmZmljTWF0Y2hbMl0udG9VcHBlckNhc2UoKX1gO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRmFsbGJhY2s6IHBhcnNlIGZyb20gZGVzY3JpcHRpb24gaWYgc3BlY2lmaWNhdGlvbnMgYXJlIGVtcHR5XHJcbiAgICBpZiAoY29yZXMgPT09IDAgJiYgZGVzY3JpcHRpb24pIHtcclxuICAgICAgY29uc3QgZGVzY0xvd2VyID0gZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKTtcclxuICAgICAgY29uc3QgY3B1TWF0Y2ggPSBkZXNjcmlwdGlvbi5tYXRjaCgvKFxcZCspXFxzKihjcHV8Y29yZXx2Y3B1KS9pKTtcclxuICAgICAgaWYgKGNwdU1hdGNoKSBjb3JlcyA9IHBhcnNlSW50KGNwdU1hdGNoWzFdKTtcclxuXHJcbiAgICAgIGNvbnN0IHJhbU1hdGNoID0gZGVzY3JpcHRpb24ubWF0Y2goLyhcXGQrKVxccypnYlxccypyYW0vaSk7XHJcbiAgICAgIGlmIChyYW1NYXRjaCkgcmFtID0gYCR7cmFtTWF0Y2hbMV19IEdCYDtcclxuXHJcbiAgICAgIGNvbnN0IHN0b3JhZ2VNYXRjaCA9IGRlc2NyaXB0aW9uLm1hdGNoKC8oXFxkKylcXHMqZ2JcXHMqKHN0b3JhZ2V8ZGlza3xzc2R8bnZtZSkvaSk7XHJcbiAgICAgIGlmIChzdG9yYWdlTWF0Y2gpIHtcclxuICAgICAgICBjb25zdCBzdG9yYWdlVHlwZSA9IGRlc2NMb3dlci5pbmNsdWRlcyhcIm52bWVcIikgPyBcIk5WTWVcIiA6IGRlc2NMb3dlci5pbmNsdWRlcyhcInNzZFwiKSA/IFwiU1NEXCIgOiBcIlwiO1xyXG4gICAgICAgIHN0b3JhZ2UgPSBgJHtzdG9yYWdlTWF0Y2hbMV19IEdCICR7c3RvcmFnZVR5cGV9YC50cmltKCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4geyBjb3JlcywgcmFtLCBzdG9yYWdlLCB0cmFmZmljIH07XHJcbiAgfTtcclxuXHJcbiAgLy8gRmV0Y2ggVlBTIHBhY2thZ2VzIGFuZCBmaW5kIHRoZSBzZWxlY3RlZCBvbmVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hWUFNQYWNrYWdlcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgICAgICAvLyBSw6ljdXDDqXJlciBsZXMgcGFja2FnZXMgVlBTIGRlcHVpcyBsYSBiYXNlIGRlIGRvbm7DqWVzXHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBwYWNrYWdlU2VydmljZS5nZXRQYWNrYWdlcyhcIlZQUyBIb3N0aW5nXCIpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiVlBTIHBhY2thZ2VzIHJlc3BvbnNlOlwiLCByZXNwb25zZSk7XHJcblxyXG4gICAgICAgIGxldCB2cHNQYWNrYWdlcyA9IFtdO1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcclxuICAgICAgICAgIHZwc1BhY2thZ2VzID0gcmVzcG9uc2UuZGF0YTtcclxuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5wYWNrYWdlcyAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEucGFja2FnZXMpKSB7XHJcbiAgICAgICAgICB2cHNQYWNrYWdlcyA9IHJlc3BvbnNlLmRhdGEucGFja2FnZXM7XHJcbiAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlKSkge1xyXG4gICAgICAgICAgdnBzUGFja2FnZXMgPSByZXNwb25zZTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFRyYW5zZm9ybWVyIGxlcyBwYWNrYWdlcyBkZSBsYSBiYXNlIGRlIGRvbm7DqWVzXHJcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWRQbGFucyA9IHZwc1BhY2thZ2VzLm1hcChwa2cgPT4ge1xyXG4gICAgICAgICAgY29uc3Qgc3BlY3MgPSBwYXJzZVNwZWNpZmljYXRpb25zKHBrZy5zcGVjaWZpY2F0aW9ucywgcGtnLmRlc2NyaXB0aW9uKTtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGlkOiBwa2cuX2lkLFxyXG4gICAgICAgICAgICBfaWQ6IHBrZy5faWQsXHJcbiAgICAgICAgICAgIG5hbWU6IHBrZy5uYW1lLFxyXG4gICAgICAgICAgICBwcmljZTogcGtnLnByaWNlLFxyXG4gICAgICAgICAgICBjb3Jlczogc3BlY3MuY29yZXMsXHJcbiAgICAgICAgICAgIHJhbTogc3BlY3MucmFtLFxyXG4gICAgICAgICAgICBzdG9yYWdlOiBzcGVjcy5zdG9yYWdlLFxyXG4gICAgICAgICAgICB0cmFmZmljOiBzcGVjcy50cmFmZmljLFxyXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogcGtnLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICBzcGVjaWZpY2F0aW9uczogcGtnLnNwZWNpZmljYXRpb25zXHJcbiAgICAgICAgICB9O1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBzZXRWcHNQbGFucyh0cmFuc2Zvcm1lZFBsYW5zKTtcclxuXHJcbiAgICAgICAgLy8gVHJvdXZlciBsZSBwYWNrYWdlIHPDqWxlY3Rpb25uw6kgcGFyIHNvbiBJRFxyXG4gICAgICAgIGlmIChwbGFuSWQgJiYgdHJhbnNmb3JtZWRQbGFucy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICBjb25zdCBmb3VuZFBsYW4gPSB0cmFuc2Zvcm1lZFBsYW5zLmZpbmQocGxhbiA9PlxyXG4gICAgICAgICAgICBwbGFuLl9pZCA9PT0gcGxhbklkIHx8XHJcbiAgICAgICAgICAgIHBsYW4uaWQgPT09IHBsYW5JZCB8fFxyXG4gICAgICAgICAgICBwbGFuLl9pZD8udG9TdHJpbmcoKSA9PT0gcGxhbklkIHx8XHJcbiAgICAgICAgICAgIHBsYW4uaWQ/LnRvU3RyaW5nKCkgPT09IHBsYW5JZFxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIGlmIChmb3VuZFBsYW4pIHtcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRQbGFuKGZvdW5kUGxhbik7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiU2VsZWN0ZWQgcGxhbiBmb3VuZDpcIiwgZm91bmRQbGFuKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJQbGFuIG5vdCBmb3VuZCB3aXRoIElEOlwiLCBwbGFuSWQpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIkF2YWlsYWJsZSBwbGFuczpcIiwgdHJhbnNmb3JtZWRQbGFucy5tYXAocCA9PiAoeyBpZDogcC5pZCwgX2lkOiBwLl9pZCwgbmFtZTogcC5uYW1lIH0pKSk7XHJcbiAgICAgICAgICAgIHNldEVycm9yKFwiUGFja2FnZSBWUFMgbm9uIHRyb3V2w6lcIik7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgVlBTIHBsYW5zOlwiLCBlcnJvcik7XHJcbiAgICAgICAgc2V0RXJyb3IoXCJFcnJldXIgbG9ycyBkdSBjaGFyZ2VtZW50IGRlcyBwbGFucyBWUFNcIik7XHJcbiAgICAgICAgc2V0VnBzUGxhbnMoW10pO1xyXG4gICAgICAgIHNldFNlbGVjdGVkUGxhbihudWxsKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBGZXRjaCBkeW5hbWljIE9TIGltYWdlcyBmcm9tIEFQSVxyXG4gICAgY29uc3QgZmV0Y2hPU0ltYWdlcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIvCflI0gRmV0Y2hpbmcgT1MgaW1hZ2VzIGZyb20gQVBJLi4uXCIpO1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdnBzU2VydmljZS5nZXRJbWFnZXMoJ2NvbnRhYm8nKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIuKchSBPUyBJbWFnZXMgcmVzcG9uc2U6XCIsIHJlc3BvbnNlKTtcclxuXHJcbiAgICAgICAgbGV0IGltYWdlcyA9IFtdO1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmRhdGEpKSB7XHJcbiAgICAgICAgICBpbWFnZXMgPSByZXNwb25zZS5kYXRhLmRhdGE7XHJcbiAgICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuaW1hZ2VzICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5pbWFnZXMpKSB7XHJcbiAgICAgICAgICBpbWFnZXMgPSByZXNwb25zZS5kYXRhLmltYWdlcztcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIFRyYW5zZm9ybSBBUEkgZGF0YSB0byBleHBlY3RlZCBmb3JtYXRcclxuICAgICAgICBjb25zdCB0cmFuc2Zvcm1lZEltYWdlcyA9IGltYWdlcy5tYXAoaW1nID0+ICh7XHJcbiAgICAgICAgICBpZDogaW1nLmltYWdlSWQgfHwgaW1nLmlkLFxyXG4gICAgICAgICAgbmFtZTogaW1nLm5hbWUsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogaW1nLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgdHlwZTogaW1nLm9zVHlwZSB8fCAnbGludXgnLFxyXG4gICAgICAgICAgdmVyc2lvbjogaW1nLnZlcnNpb24sXHJcbiAgICAgICAgICBwcm92aWRlcjogaW1nLnByb3ZpZGVyXHJcbiAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZyhcIvCflIQgU2V0dGluZyBPUyBpbWFnZXM6XCIsIHRyYW5zZm9ybWVkSW1hZ2VzLmxlbmd0aCwgXCJpbWFnZXNcIik7XHJcbiAgICAgICAgc2V0T3NJbWFnZXModHJhbnNmb3JtZWRJbWFnZXMpO1xyXG5cclxuICAgICAgICAvLyBTZXQgZGVmYXVsdCBPUyBpZiBhdmFpbGFibGVcclxuICAgICAgICBpZiAodHJhbnNmb3JtZWRJbWFnZXMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgY29uc3QgZGVmYXVsdE9TID0gdHJhbnNmb3JtZWRJbWFnZXMuZmluZChpbWcgPT5cclxuICAgICAgICAgICAgaW1nLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygndWJ1bnR1JykgJiZcclxuICAgICAgICAgICAgaW1nLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnMjIuMDQnKVxyXG4gICAgICAgICAgKSB8fCB0cmFuc2Zvcm1lZEltYWdlc1swXTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwi8J+UhCBTZXR0aW5nIGRlZmF1bHQgT1M6XCIsIGRlZmF1bHRPUy5uYW1lLCBkZWZhdWx0T1MuaWQpO1xyXG4gICAgICAgICAgc2V0U2VsZWN0ZWRPUyhkZWZhdWx0T1MuaWQpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwi4p2MIEVycm9yIGZldGNoaW5nIE9TIGltYWdlczpcIiwgZXJyb3IpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZXRhaWxzOlwiLCBlcnJvci5yZXNwb25zZT8uZGF0YSB8fCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgICAvLyBGYWxsYmFjayB0byBzdGF0aWMgZGF0YSBpZiBBUEkgZmFpbHNcclxuICAgICAgICBjb25zdCBzdGF0aWNPc0ltYWdlcyA9IFtcclxuICAgICAgICAgIHsgaWQ6IFwidWJ1bnR1LTIwLjA0XCIsIG5hbWU6IFwiVWJ1bnR1IDIwLjA0IExUU1wiLCB0eXBlOiBcInVidW50dVwiIH0sXHJcbiAgICAgICAgICB7IGlkOiBcInVidW50dS0yMi4wNFwiLCBuYW1lOiBcIlVidW50dSAyMi4wNCBMVFNcIiwgdHlwZTogXCJ1YnVudHVcIiB9LFxyXG4gICAgICAgICAgeyBpZDogXCJjZW50b3MtN1wiLCBuYW1lOiBcIkNlbnRPUyA3XCIsIHR5cGU6IFwiY2VudG9zXCIgfSxcclxuICAgICAgICAgIHsgaWQ6IFwiZGViaWFuLTExXCIsIG5hbWU6IFwiRGViaWFuIDExXCIsIHR5cGU6IFwiZGViaWFuXCIgfVxyXG4gICAgICAgIF07XHJcbiAgICAgICAgc2V0T3NJbWFnZXMoc3RhdGljT3NJbWFnZXMpO1xyXG4gICAgICAgIHNldFNlbGVjdGVkT1MoXCJ1YnVudHUtMjIuMDRcIik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8gRmV0Y2ggZHluYW1pYyByZWdpb25zIGZyb20gQVBJXHJcbiAgICBjb25zdCBmZXRjaFJlZ2lvbnMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIEZldGNoaW5nIHJlZ2lvbnMgZnJvbSBBUEkuLi5cIik7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB2cHNTZXJ2aWNlLmdldFJlZ2lvbnMoJ2NvbnRhYm8nKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIuKchSBSZWdpb25zIHJlc3BvbnNlOlwiLCByZXNwb25zZSk7XHJcblxyXG4gICAgICAgIGxldCByZWdpb25zID0gW107XHJcbiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YSkpIHtcclxuICAgICAgICAgIHJlZ2lvbnMgPSByZXNwb25zZS5kYXRhLmRhdGE7XHJcbiAgICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEucmVnaW9ucyAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEucmVnaW9ucykpIHtcclxuICAgICAgICAgIHJlZ2lvbnMgPSByZXNwb25zZS5kYXRhLnJlZ2lvbnM7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBUcmFuc2Zvcm0gQVBJIGRhdGEgdG8gZXhwZWN0ZWQgZm9ybWF0XHJcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWRSZWdpb25zID0gcmVnaW9ucy5tYXAocmVnaW9uID0+ICh7XHJcbiAgICAgICAgICBpZDogcmVnaW9uLnJlZ2lvblNsdWcsXHJcbiAgICAgICAgICBuYW1lOiByZWdpb24ucmVnaW9uTmFtZSxcclxuICAgICAgICAgIHByb3ZpZGVyOiByZWdpb24ucHJvdmlkZXJcclxuICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UhCBTZXR0aW5nIHJlZ2lvbnM6XCIsIHRyYW5zZm9ybWVkUmVnaW9ucy5sZW5ndGgsIFwicmVnaW9uc1wiKTtcclxuICAgICAgICBzZXRSZWdpb25zKHRyYW5zZm9ybWVkUmVnaW9ucyk7XHJcblxyXG4gICAgICAgIC8vIFNldCBkZWZhdWx0IHJlZ2lvbiBpZiBhdmFpbGFibGVcclxuICAgICAgICBpZiAodHJhbnNmb3JtZWRSZWdpb25zLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIGNvbnN0IGRlZmF1bHRSZWdpb24gPSB0cmFuc2Zvcm1lZFJlZ2lvbnMuZmluZChyZWdpb24gPT5cclxuICAgICAgICAgICAgcmVnaW9uLmlkID09PSAnRVUnXHJcbiAgICAgICAgICApIHx8IHRyYW5zZm9ybWVkUmVnaW9uc1swXTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwi8J+UhCBTZXR0aW5nIGRlZmF1bHQgcmVnaW9uOlwiLCBkZWZhdWx0UmVnaW9uLm5hbWUsIGRlZmF1bHRSZWdpb24uaWQpO1xyXG4gICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbihkZWZhdWx0UmVnaW9uLmlkKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBFcnJvciBmZXRjaGluZyByZWdpb25zOlwiLCBlcnJvcik7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRldGFpbHM6XCIsIGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IGVycm9yLm1lc3NhZ2UpO1xyXG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIHN0YXRpYyBkYXRhIGlmIEFQSSBmYWlsc1xyXG4gICAgICAgIGNvbnN0IHN0YXRpY1JlZ2lvbnMgPSBbXHJcbiAgICAgICAgICB7IGlkOiBcIkVVXCIsIG5hbWU6IFwiRXVyb3BlYW4gVW5pb25cIiwgZGVzY3JpcHRpb246IFwiR2VybWFueVwiLCBjb3VudHJ5OiBcIkdlcm1hbnlcIiwgY2l0eTogXCJOdXJlbWJlcmdcIiB9LFxyXG4gICAgICAgICAgeyBpZDogXCJVUy1jZW50cmFsXCIsIG5hbWU6IFwiVW5pdGVkIFN0YXRlcyBDZW50cmFsXCIsIGRlc2NyaXB0aW9uOiBcIlN0LiBMb3Vpc1wiLCBjb3VudHJ5OiBcIlVuaXRlZCBTdGF0ZXNcIiwgY2l0eTogXCJTdC4gTG91aXNcIiB9LFxyXG4gICAgICAgICAgeyBpZDogXCJTR1wiLCBuYW1lOiBcIkFzaWEgUGFjaWZpY1wiLCBkZXNjcmlwdGlvbjogXCJTaW5nYXBvcmVcIiwgY291bnRyeTogXCJTaW5nYXBvcmVcIiwgY2l0eTogXCJTaW5nYXBvcmVcIiB9XHJcbiAgICAgICAgXTtcclxuICAgICAgICBzZXRSZWdpb25zKHN0YXRpY1JlZ2lvbnMpO1xyXG4gICAgICAgIHNldFNlbGVjdGVkTG9jYXRpb24oXCJFVVwiKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBBcHBlbGVyIGxlcyBmb25jdGlvbnMgcG91ciByw6ljdXDDqXJlciBsZXMgZG9ubsOpZXNcclxuICAgIGZldGNoVlBTUGFja2FnZXMoKTtcclxuICAgIGZldGNoT1NJbWFnZXMoKTtcclxuICAgIGZldGNoUmVnaW9ucygpO1xyXG5cclxuXHJcbiAgfSwgW3BsYW5JZF0pO1xyXG5cclxuICAvLyBDb25maWd1cmF0aW9uIHN0YXRlXHJcbiAgY29uc3QgW3NlbGVjdGVkT1MsIHNldFNlbGVjdGVkT1NdID0gdXNlU3RhdGUoXCJ1YnVudHUtMjAuMDRcIik7XHJcbiAgY29uc3QgW3NlbGVjdGVkTG9jYXRpb24sIHNldFNlbGVjdGVkTG9jYXRpb25dID0gdXNlU3RhdGUoXCJmcmFuY2VcIik7XHJcbiAgY29uc3QgW3NlbGVjdGVkUGVyaW9kLCBzZXRTZWxlY3RlZFBlcmlvZF0gPSB1c2VTdGF0ZShcIm1vbnRobHlcIik7XHJcbiAgY29uc3QgW2FkZGl0aW9uYWxJUHMsIHNldEFkZGl0aW9uYWxJUHNdID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgW2JhY2t1cEVuYWJsZWQsIHNldEJhY2t1cEVuYWJsZWRdID0gdXNlU3RhdGUoYXV0b0JhY2t1cCk7XHJcbiAgY29uc3QgW3F1YW50aXR5LCBzZXRRdWFudGl0eV0gPSB1c2VTdGF0ZSgxKTtcclxuXHJcbiAgLy8gTmV3IENvbnRhYm8tc3R5bGUgb3B0aW9uc1xyXG4gIGNvbnN0IFthdXRvQmFja3VwT3B0aW9uLCBzZXRBdXRvQmFja3VwT3B0aW9uXSA9IHVzZVN0YXRlKFwibm9uZVwiKTtcclxuICBjb25zdCBbcHJpdmF0ZU5ldHdvcmtpbmcsIHNldFByaXZhdGVOZXR3b3JraW5nXSA9IHVzZVN0YXRlKFwibm9uZVwiKTtcclxuICBjb25zdCBbaXB2NEFkZHJlc3Nlcywgc2V0SXB2NEFkZHJlc3Nlc10gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbb2JqZWN0U3RvcmFnZSwgc2V0T2JqZWN0U3RvcmFnZV0gPSB1c2VTdGF0ZShcIm5vbmVcIik7XHJcbiAgY29uc3QgW3NlcnZlck1hbmFnZW1lbnQsIHNldFNlcnZlck1hbmFnZW1lbnRdID0gdXNlU3RhdGUoXCJ1bm1hbmFnZWRcIik7XHJcbiAgY29uc3QgW21vbml0b3JpbmcsIHNldE1vbml0b3JpbmddID0gdXNlU3RhdGUoXCJub25lXCIpO1xyXG4gIGNvbnN0IFtzc2wsIHNldFNzbF0gPSB1c2VTdGF0ZShcIm5vbmVcIik7XHJcblxyXG4gIC8vIExvZ2luIGNyZWRlbnRpYWxzIHN0YXRlXHJcbiAgY29uc3QgW3VzZXJuYW1lLCBzZXRVc2VybmFtZV0gPSB1c2VTdGF0ZShcInJvb3RcIik7XHJcbiAgY29uc3QgW3Bhc3N3b3JkLCBzZXRQYXNzd29yZF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc2hvd1Bhc3N3b3JkLCBzZXRTaG93UGFzc3dvcmRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwYXNzd29yZEVycm9yLCBzZXRQYXNzd29yZEVycm9yXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG5cclxuICAvLyBQYXNzd29yZCB2YWxpZGF0aW9uIGZ1bmN0aW9uXHJcbiAgY29uc3QgdmFsaWRhdGVQYXNzd29yZCA9IChwd2QpID0+IHtcclxuICAgIGlmICghcHdkKSB7XHJcbiAgICAgIHJldHVybiBcIlBsZWFzZSBlbnRlciBhIHZhbGlkIHBhc3N3b3JkXCI7XHJcbiAgICB9XHJcbiAgICBpZiAocHdkLmxlbmd0aCA8IDgpIHtcclxuICAgICAgcmV0dXJuIFwiUGFzc3dvcmQgbXVzdCBiZSBhdCBsZWFzdCA4IGNoYXJhY3RlcnMgbG9uZ1wiO1xyXG4gICAgfVxyXG4gICAgaWYgKCEvKD89LipbYS16XSkvLnRlc3QocHdkKSkge1xyXG4gICAgICByZXR1cm4gXCJQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIGxvd2VyY2FzZSBsZXR0ZXJcIjtcclxuICAgIH1cclxuICAgIGlmICghLyg/PS4qW0EtWl0pLy50ZXN0KHB3ZCkpIHtcclxuICAgICAgcmV0dXJuIFwiUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSB1cHBlcmNhc2UgbGV0dGVyXCI7XHJcbiAgICB9XHJcbiAgICBpZiAoIS8oPz0uKlxcZCkvLnRlc3QocHdkKSkge1xyXG4gICAgICByZXR1cm4gXCJQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIG51bWJlclwiO1xyXG4gICAgfVxyXG4gICAgaWYgKCEvKD89LipbIUAjJCVeJiooKV8rXFwtPVxcW1xcXXt9Oyc6XCJcXFxcfCwuPD5cXC8/XSkvLnRlc3QocHdkKSkge1xyXG4gICAgICByZXR1cm4gXCJQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHNwZWNpYWwgY2hhcmFjdGVyXCI7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gXCJcIjtcclxuICB9O1xyXG5cclxuICAvLyBHZW5lcmF0ZSBzZWN1cmUgcGFzc3dvcmQgZnVuY3Rpb25cclxuICBjb25zdCBnZW5lcmF0ZVNlY3VyZVBhc3N3b3JkID0gKCkgPT4ge1xyXG4gICAgY29uc3QgbG93ZXJjYXNlID0gJ2FiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6JztcclxuICAgIGNvbnN0IHVwcGVyY2FzZSA9ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWic7XHJcbiAgICBjb25zdCBudW1iZXJzID0gJzAxMjM0NTY3ODknO1xyXG4gICAgY29uc3Qgc3ltYm9scyA9ICchQCMkJV4mKigpXystPVtde318OzosLjw+Pyc7XHJcbiAgICBjb25zdCBhbGxDaGFycyA9IGxvd2VyY2FzZSArIHVwcGVyY2FzZSArIG51bWJlcnMgKyBzeW1ib2xzO1xyXG5cclxuICAgIGxldCBuZXdQYXNzd29yZCA9ICcnO1xyXG5cclxuICAgIC8vIEVuc3VyZSBhdCBsZWFzdCBvbmUgY2hhcmFjdGVyIGZyb20gZWFjaCBjYXRlZ29yeVxyXG4gICAgbmV3UGFzc3dvcmQgKz0gbG93ZXJjYXNlW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGxvd2VyY2FzZS5sZW5ndGgpXTtcclxuICAgIG5ld1Bhc3N3b3JkICs9IHVwcGVyY2FzZVtNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiB1cHBlcmNhc2UubGVuZ3RoKV07XHJcbiAgICBuZXdQYXNzd29yZCArPSBudW1iZXJzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIG51bWJlcnMubGVuZ3RoKV07XHJcbiAgICBuZXdQYXNzd29yZCArPSBzeW1ib2xzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHN5bWJvbHMubGVuZ3RoKV07XHJcblxyXG4gICAgLy8gRmlsbCB0aGUgcmVzdCByYW5kb21seVxyXG4gICAgZm9yIChsZXQgaSA9IDQ7IGkgPCAxNjsgaSsrKSB7XHJcbiAgICAgIG5ld1Bhc3N3b3JkICs9IGFsbENoYXJzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGFsbENoYXJzLmxlbmd0aCldO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNodWZmbGUgdGhlIHBhc3N3b3JkXHJcbiAgICBuZXdQYXNzd29yZCA9IG5ld1Bhc3N3b3JkLnNwbGl0KCcnKS5zb3J0KCgpID0+IE1hdGgucmFuZG9tKCkgLSAwLjUpLmpvaW4oJycpO1xyXG5cclxuICAgIHNldFBhc3N3b3JkKG5ld1Bhc3N3b3JkKTtcclxuICAgIHNldFBhc3N3b3JkRXJyb3IoXCJcIik7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHBhc3N3b3JkIGNoYW5nZVxyXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkQ2hhbmdlID0gKGUpID0+IHtcclxuICAgIGNvbnN0IG5ld1Bhc3N3b3JkID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICBzZXRQYXNzd29yZChuZXdQYXNzd29yZCk7XHJcbiAgICBzZXRQYXNzd29yZEVycm9yKHZhbGlkYXRlUGFzc3dvcmQobmV3UGFzc3dvcmQpKTtcclxuICB9O1xyXG5cclxuICAvLyBHZW5lcmF0ZSBpbml0aWFsIHBhc3N3b3JkIG9uIGNvbXBvbmVudCBtb3VudFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIXBhc3N3b3JkKSB7XHJcbiAgICAgIGdlbmVyYXRlU2VjdXJlUGFzc3dvcmQoKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIEhhbmRsZSBhZGRpbmcgVlBTIHRvIGNhcnRcclxuICBjb25zdCBoYW5kbGVBZGRUb0NhcnQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNlbGVjdGVkUGxhbikge1xyXG4gICAgICB0b2FzdC5lcnJvcihcIlZldWlsbGV6IHPDqWxlY3Rpb25uZXIgdW4gcGxhbiBWUFNcIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBWYWxpZGF0ZSBwYXNzd29yZFxyXG4gICAgY29uc3QgcGFzc3dvcmRWYWxpZGF0aW9uRXJyb3IgPSB2YWxpZGF0ZVBhc3N3b3JkKHBhc3N3b3JkKTtcclxuICAgIGlmIChwYXNzd29yZFZhbGlkYXRpb25FcnJvcikge1xyXG4gICAgICBzZXRQYXNzd29yZEVycm9yKHBhc3N3b3JkVmFsaWRhdGlvbkVycm9yKTtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJQbGVhc2UgZW50ZXIgYSB2YWxpZCBwYXNzd29yZFwiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHNldE9yZGVyTG9hZGluZyh0cnVlKTtcclxuXHJcbiAgICAgIC8vIFbDqXJpZmllciBzaSBub3VzIGF2b25zIHVuIElEIHZhbGlkZVxyXG4gICAgICBjb25zdCBwYWNrYWdlSWQgPSBzZWxlY3RlZFBsYW4uX2lkIHx8IHNlbGVjdGVkUGxhbi5pZDtcclxuXHJcbiAgICAgIGlmICghcGFja2FnZUlkKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXCJJRCBkdSBwYWNrYWdlIFZQUyBtYW5xdWFudC4gVmV1aWxsZXogcsOpZXNzYXllci5cIik7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBNYXAgZnJvbnRlbmQgc2VsZWN0aW9ucyB0byBDb250YWJvIEFQSSBmb3JtYXRcclxuICAgICAgY29uc3QgY29udGFib1JlZ2lvbk1hcCA9IHtcclxuICAgICAgICAnZnJhbmNlJzogJ0VVJyxcclxuICAgICAgICAnRVUnOiAnRVUnLFxyXG4gICAgICAgICdnZXJtYW55JzogJ0VVJyxcclxuICAgICAgICAnVVMtY2VudHJhbCc6ICdVUy1lYXN0JyxcclxuICAgICAgICAndXNhJzogJ1VTLWVhc3QnLFxyXG4gICAgICAgICdTRyc6ICdTSU4nLFxyXG4gICAgICAgICdzaW5nYXBvcmUnOiAnU0lOJyxcclxuICAgICAgICAnYXNpYSc6ICdTSU4nXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCBjb250YWJvT1NNYXAgPSB7XHJcbiAgICAgICAgJ3VidW50dS0yMC4wNCc6ICd1YnVudHUtMjAuMDQnLFxyXG4gICAgICAgICd1YnVudHUtMjIuMDQnOiAndWJ1bnR1LTIyLjA0JyxcclxuICAgICAgICAndWJ1bnR1LTI0LjA0JzogJ3VidW50dS0yNC4wNCcsXHJcbiAgICAgICAgJ2NlbnRvcy03JzogJ2NlbnRvcy03JyxcclxuICAgICAgICAnY2VudG9zLTgnOiAnY2VudG9zLTgnLFxyXG4gICAgICAgICdkZWJpYW4tMTAnOiAnZGViaWFuLTEwJyxcclxuICAgICAgICAnZGViaWFuLTExJzogJ2RlYmlhbi0xMScsXHJcbiAgICAgICAgJ3dpbmRvd3MtMjAxOSc6ICd3aW5kb3dzLXNlcnZlci0yMDE5JyxcclxuICAgICAgICAnd2luZG93cy0yMDIyJzogJ3dpbmRvd3Mtc2VydmVyLTIwMjInXHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBHZW5lcmF0ZSBkaXNwbGF5IG5hbWUgaWYgbm90IHByb3ZpZGVkXHJcbiAgICAgIGNvbnN0IGRpc3BsYXlOYW1lID0gYCR7c2VsZWN0ZWRQbGFuLm5hbWV9LSR7RGF0ZS5ub3coKX1gO1xyXG5cclxuICAgICAgLy8gUHLDqXBhcmVyIGxlcyBkb25uw6llcyBwb3VyIGwnYWpvdXQgYXUgcGFuaWVyIGF2ZWMgY29uZmlndXJhdGlvbiBDb250YWJvXHJcbiAgICAgIGNvbnN0IGNhcnREYXRhID0ge1xyXG4gICAgICAgIHBhY2thZ2VJZDogcGFja2FnZUlkLFxyXG4gICAgICAgIHF1YW50aXR5OiBxdWFudGl0eSxcclxuICAgICAgICBwZXJpb2Q6IHNlbGVjdGVkUGVyaW9kID09PSAnbW9udGhseScgPyAxIDogc2VsZWN0ZWRQZXJpb2QgPT09ICc2bW9udGhzJyA/IDYgOiAxMixcclxuICAgICAgICAvLyBDb25maWd1cmF0aW9uIHBlcnNvbm5hbGlzw6llIHBvdXIgQ29udGFibyBWUFNcclxuICAgICAgICBjdXN0b21Db25maWd1cmF0aW9uOiB7XHJcbiAgICAgICAgICAvLyBDb250YWJvIEFQSSBmaWVsZHNcclxuICAgICAgICAgIHBsYW5JZDogc2VsZWN0ZWRQbGFuLnZwc0NvbmZpZz8ucHJvdmlkZXJQcm9kdWN0SWQgfHwgc2VsZWN0ZWRQbGFuLmlkLCAvLyBWOTEsIFY5MiwgZXRjLlxyXG4gICAgICAgICAgcHJvdmlkZXI6ICdjb250YWJvJyxcclxuICAgICAgICAgIHJlZ2lvbjogY29udGFib1JlZ2lvbk1hcFtzZWxlY3RlZExvY2F0aW9uXSB8fCAnRVUnLFxyXG4gICAgICAgICAgb3BlcmF0aW5nU3lzdGVtOiBjb250YWJvT1NNYXBbc2VsZWN0ZWRPU10gfHwgc2VsZWN0ZWRPUyxcclxuICAgICAgICAgIGRpc3BsYXlOYW1lOiBkaXNwbGF5TmFtZSxcclxuICAgICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSxcclxuICAgICAgICAgIHJvb3RQYXNzd29yZDogcGFzc3dvcmQsXHJcbiAgICAgICAgICBzc2hLZXlzOiBbXSwgLy8gV2lsbCBiZSBhZGRlZCBsYXRlciBpZiB1c2VyIHByb3ZpZGVzXHJcbiAgICAgICAgICB1c2VyRGF0YTogJycsIC8vIENsb3VkLWluaXQgc2NyaXB0IGlmIG5lZWRlZFxyXG4gICAgICAgICAgYWRkb25zOiB7XHJcbiAgICAgICAgICAgIHByaXZhdGVuZXR3b3JraW5nOiBwcml2YXRlTmV0d29ya2luZyAhPT0gJ25vbmUnLFxyXG4gICAgICAgICAgICBhdXRvYmFja3VwOiBhdXRvQmFja3VwT3B0aW9uICE9PSAnbm9uZScsXHJcbiAgICAgICAgICAgIG1vbml0b3Jpbmc6IG1vbml0b3JpbmcgIT09ICdub25lJ1xyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIC8vIFBsYW4gc3BlY2lmaWNhdGlvbnMgZm9yIHJlZmVyZW5jZVxyXG4gICAgICAgICAgY3B1OiBzZWxlY3RlZFBsYW4uY29yZXMgfHwgc2VsZWN0ZWRQbGFuLmNwdSxcclxuICAgICAgICAgIHJhbTogc2VsZWN0ZWRQbGFuLnJhbSxcclxuICAgICAgICAgIHN0b3JhZ2U6IHNlbGVjdGVkUGxhbi5zdG9yYWdlLFxyXG4gICAgICAgICAgYmFuZHdpZHRoOiBzZWxlY3RlZFBsYW4udHJhZmZpYyB8fCBzZWxlY3RlZFBsYW4uYmFuZHdpZHRoLFxyXG4gICAgICAgICAgLy8gRnJvbnRlbmQtc3BlY2lmaWMgZmllbGRzIGZvciBkaXNwbGF5XHJcbiAgICAgICAgICBmcm9udGVuZENvbmZpZzoge1xyXG4gICAgICAgICAgICBvcGVyYXRpbmdTeXN0ZW06IHNlbGVjdGVkT1MsXHJcbiAgICAgICAgICAgIGxvY2F0aW9uOiBzZWxlY3RlZExvY2F0aW9uLFxyXG4gICAgICAgICAgICBhZGRpdGlvbmFsSVBzOiBhZGRpdGlvbmFsSVBzLFxyXG4gICAgICAgICAgICBiYWNrdXA6IGlzQXV0b0JhY2t1cCB8fCBiYWNrdXBFbmFibGVkLFxyXG4gICAgICAgICAgICBwbGFuTmFtZTogc2VsZWN0ZWRQbGFuLm5hbWUsXHJcbiAgICAgICAgICAgIGF1dG9CYWNrdXBPcHRpb246IGF1dG9CYWNrdXBPcHRpb24sXHJcbiAgICAgICAgICAgIHByaXZhdGVOZXR3b3JraW5nOiBwcml2YXRlTmV0d29ya2luZyxcclxuICAgICAgICAgICAgaXB2NEFkZHJlc3NlczogaXB2NEFkZHJlc3NlcyxcclxuICAgICAgICAgICAgb2JqZWN0U3RvcmFnZTogb2JqZWN0U3RvcmFnZSxcclxuICAgICAgICAgICAgc2VydmVyTWFuYWdlbWVudDogc2VydmVyTWFuYWdlbWVudCxcclxuICAgICAgICAgICAgbW9uaXRvcmluZzogbW9uaXRvcmluZyxcclxuICAgICAgICAgICAgc3NsOiBzc2xcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIkFkZGluZyBWUFMgdG8gY2FydDpcIiwgY2FydERhdGEpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlNlbGVjdGVkIHBsYW46XCIsIHNlbGVjdGVkUGxhbik7XHJcblxyXG4gICAgICAvLyBBam91dGVyIGF1IHBhbmllciB2aWEgbGUgc2VydmljZVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNhcnRTZXJ2aWNlLmFkZEl0ZW1Ub0NhcnQoY2FydERhdGEpO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coXCJDYXJ0IHJlc3BvbnNlOlwiLCByZXNwb25zZSk7XHJcblxyXG4gICAgICAvLyBNZXR0cmUgw6Agam91ciBsZSBjb21wdGV1ciBkdSBwYW5pZXJcclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGE/LmNhcnQ/LmNhcnRDb3VudCkge1xyXG4gICAgICAgIHNldENhcnRDb3VudChyZXNwb25zZS5kYXRhLmNhcnQuY2FydENvdW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQWZmaWNoZXIgbGUgbWVzc2FnZSBkZSBzdWNjw6hzXHJcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoYCR7c2VsZWN0ZWRQbGFuLm5hbWV9IGFqb3V0w6kgYXUgcGFuaWVyIGF2ZWMgc3VjY8OocyFgKTtcclxuXHJcbiAgICAgIC8vIFJlZGlyaWdlciB2ZXJzIGxlIHBhbmllclxyXG4gICAgICByb3V0ZXIucHVzaCgnL2NsaWVudC9jYXJ0Jyk7XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFkZGluZyBWUFMgdG8gY2FydDpcIiwgZXJyb3IpO1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgcmVzcG9uc2U6XCIsIGVycm9yLnJlc3BvbnNlKTtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGRhdGE6XCIsIGVycm9yLnJlc3BvbnNlPy5kYXRhKTtcclxuXHJcbiAgICAgIC8vIEdlc3Rpb24gZGVzIGVycmV1cnMgc3DDqWNpZmlxdWVzXHJcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSkge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZSk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDA0KSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXCJQYWNrYWdlIFZQUyBub24gdHJvdXbDqS4gVmV1aWxsZXogY29udGFjdGVyIGxlIHN1cHBvcnQuXCIpO1xyXG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKFwiVmV1aWxsZXogdm91cyBjb25uZWN0ZXIgcG91ciBham91dGVyIGF1IHBhbmllci5cIik7XHJcbiAgICAgICAgcm91dGVyLnB1c2goJy9hdXRoL2xvZ2luJyk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAwKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXCJEb25uw6llcyBpbnZhbGlkZXMuIFZldWlsbGV6IHbDqXJpZmllciB2b3RyZSBzw6lsZWN0aW9uLlwiKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIkVycmV1ciBsb3JzIGRlIGwnYWpvdXQgYXUgcGFuaWVyLiBWZXVpbGxleiByw6llc3NheWVyLlwiKTtcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0T3JkZXJMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBVc2UgZHluYW1pYyBPUyBpbWFnZXMgZGF0YSB3aXRoIGZhbGxiYWNrIHRvIHN0YXRpYyBkYXRhXHJcbiAgY29uc3Qgb3BlcmF0aW5nU3lzdGVtcyA9IG9zSW1hZ2VzLmxlbmd0aCA+IDAgPyBvc0ltYWdlcy5tYXAob3MgPT4gKHtcclxuICAgIGlkOiBvcy5pZCxcclxuICAgIG5hbWU6IG9zLm5hbWUsXHJcbiAgICBpY29uOiBnZXRPU0ljb24ob3MudHlwZSB8fCBvcy5vc1R5cGUpLFxyXG4gICAgdHlwZTogb3MudHlwZSB8fCBvcy5vc1R5cGUgfHwgJ2xpbnV4JyxcclxuICAgIGRlc2NyaXB0aW9uOiBvcy5kZXNjcmlwdGlvbixcclxuICAgIHZlcnNpb246IG9zLnZlcnNpb24sXHJcbiAgICBwcm92aWRlcjogb3MucHJvdmlkZXJcclxuICB9KSkgOiBbXHJcbiAgICB7IGlkOiBcInVidW50dS0yMC4wNFwiLCBuYW1lOiBcIlVidW50dSAyMC4wNCBMVFNcIiwgaWNvbjogVWJ1bnR1SWNvbiwgdHlwZTogXCJsaW51eFwiIH0sXHJcbiAgICB7IGlkOiBcInVidW50dS0yMi4wNFwiLCBuYW1lOiBcIlVidW50dSAyMi4wNCBMVFNcIiwgaWNvbjogVWJ1bnR1SWNvbiwgdHlwZTogXCJsaW51eFwiIH0sXHJcbiAgICB7IGlkOiBcImNlbnRvcy04XCIsIG5hbWU6IFwiQ2VudE9TIDhcIiwgaWNvbjogQ2VudE9TSWNvbiwgdHlwZTogXCJsaW51eFwiIH0sXHJcbiAgICB7IGlkOiBcImRlYmlhbi0xMVwiLCBuYW1lOiBcIkRlYmlhbiAxMVwiLCBpY29uOiBEZWJpYW5JY29uLCB0eXBlOiBcImxpbnV4XCIgfVxyXG4gIF07XHJcblxyXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgT1MgaWNvbiBiYXNlZCBvbiB0eXBlXHJcbiAgZnVuY3Rpb24gZ2V0T1NJY29uKG9zVHlwZSkge1xyXG4gICAgY29uc3QgaWNvbk1hcCA9IHtcclxuICAgICAgJ3VidW50dSc6IFVidW50dUljb24sXHJcbiAgICAgICdjZW50b3MnOiBDZW50T1NJY29uLFxyXG4gICAgICAnZGViaWFuJzogRGViaWFuSWNvbixcclxuICAgICAgJ3dpbmRvd3MnOiBXaW5kb3dzSWNvbixcclxuICAgICAgJ2xpbnV4JzogVWJ1bnR1SWNvbiAvLyBkZWZhdWx0IGZvciBsaW51eFxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiBvc1R5cGUgY29udGFpbnMgc3BlY2lmaWMgT1MgbmFtZXNcclxuICAgIGlmIChvc1R5cGUgJiYgdHlwZW9mIG9zVHlwZSA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgY29uc3QgbG93ZXJUeXBlID0gb3NUeXBlLnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgIGlmIChsb3dlclR5cGUuaW5jbHVkZXMoJ3VidW50dScpKSByZXR1cm4gVWJ1bnR1SWNvbjtcclxuICAgICAgaWYgKGxvd2VyVHlwZS5pbmNsdWRlcygnY2VudG9zJykpIHJldHVybiBDZW50T1NJY29uO1xyXG4gICAgICBpZiAobG93ZXJUeXBlLmluY2x1ZGVzKCdkZWJpYW4nKSkgcmV0dXJuIERlYmlhbkljb247XHJcbiAgICAgIGlmIChsb3dlclR5cGUuaW5jbHVkZXMoJ3dpbmRvd3MnKSkgcmV0dXJuIFdpbmRvd3NJY29uO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBpY29uTWFwW29zVHlwZV0gfHwgVWJ1bnR1SWNvbjtcclxuICB9XHJcblxyXG4gIC8vIFVzZSBkeW5hbWljIHJlZ2lvbnMgZGF0YSB3aXRoIGZhbGxiYWNrIHRvIHN0YXRpYyBkYXRhXHJcbiAgY29uc3QgbG9jYXRpb25zID0gcmVnaW9ucy5sZW5ndGggPiAwID8gcmVnaW9ucy5tYXAocmVnaW9uID0+ICh7XHJcbiAgICBpZDogcmVnaW9uLmlkLFxyXG4gICAgbmFtZTogcmVnaW9uLm5hbWUsXHJcbiAgICBmbGFnOiBnZXRSZWdpb25GbGFnKHJlZ2lvbi5jb3VudHJ5KSxcclxuICAgIHBpbmc6IGdldFJlZ2lvblBpbmcocmVnaW9uLmlkKSxcclxuICAgIGRlc2NyaXB0aW9uOiByZWdpb24uZGVzY3JpcHRpb24sXHJcbiAgICBjaXR5OiByZWdpb24uY2l0eSxcclxuICAgIGNvdW50cnk6IHJlZ2lvbi5jb3VudHJ5XHJcbiAgfSkpIDogW1xyXG4gICAgeyBpZDogXCJFVVwiLCBuYW1lOiBcIkV1cm9wZWFuIFVuaW9uXCIsIGZsYWc6IFwi8J+HqfCfh6pcIiwgcGluZzogXCIxNW1zXCIsIGRlc2NyaXB0aW9uOiBcIkdlcm1hbnlcIiwgY2l0eTogXCJOdXJlbWJlcmdcIiwgY291bnRyeTogXCJHZXJtYW55XCIgfSxcclxuICAgIHsgaWQ6IFwiVVMtY2VudHJhbFwiLCBuYW1lOiBcIlVuaXRlZCBTdGF0ZXMgQ2VudHJhbFwiLCBmbGFnOiBcIvCfh7rwn4e4XCIsIHBpbmc6IFwiMTIwbXNcIiwgZGVzY3JpcHRpb246IFwiU3QuIExvdWlzXCIsIGNpdHk6IFwiU3QuIExvdWlzXCIsIGNvdW50cnk6IFwiVW5pdGVkIFN0YXRlc1wiIH0sXHJcbiAgICB7IGlkOiBcIlNHXCIsIG5hbWU6IFwiQXNpYSBQYWNpZmljXCIsIGZsYWc6IFwi8J+HuPCfh6xcIiwgcGluZzogXCIyMDBtc1wiLCBkZXNjcmlwdGlvbjogXCJTaW5nYXBvcmVcIiwgY2l0eTogXCJTaW5nYXBvcmVcIiwgY291bnRyeTogXCJTaW5nYXBvcmVcIiB9XHJcbiAgXTtcclxuXHJcbiAgLy8gSGVscGVyIGZ1bmN0aW9ucyBmb3IgcmVnaW9uIGRpc3BsYXlcclxuICBmdW5jdGlvbiBnZXRSZWdpb25GbGFnKGNvdW50cnkpIHtcclxuICAgIGNvbnN0IGZsYWdNYXAgPSB7XHJcbiAgICAgICdHZXJtYW55JzogJ/Cfh6nwn4eqJyxcclxuICAgICAgJ1VuaXRlZCBTdGF0ZXMnOiAn8J+HuvCfh7gnLFxyXG4gICAgICAnU2luZ2Fwb3JlJzogJ/Cfh7jwn4esJyxcclxuICAgICAgJ0ZyYW5jZSc6ICfwn4er8J+HtycsXHJcbiAgICAgICdOZXRoZXJsYW5kcyc6ICfwn4ez8J+HsSdcclxuICAgIH07XHJcbiAgICByZXR1cm4gZmxhZ01hcFtjb3VudHJ5XSB8fCAn8J+MjSc7XHJcbiAgfVxyXG5cclxuICBmdW5jdGlvbiBnZXRSZWdpb25QaW5nKHJlZ2lvbklkKSB7XHJcbiAgICBjb25zdCBwaW5nTWFwID0ge1xyXG4gICAgICAnRVUnOiAnMTVtcycsXHJcbiAgICAgICdVUy1jZW50cmFsJzogJzEyMG1zJyxcclxuICAgICAgJ1VTLWVhc3QnOiAnMTEwbXMnLFxyXG4gICAgICAnVVMtd2VzdCc6ICcxMzBtcycsXHJcbiAgICAgICdTRyc6ICcyMDBtcydcclxuICAgIH07XHJcbiAgICByZXR1cm4gcGluZ01hcFtyZWdpb25JZF0gfHwgJzUwbXMnO1xyXG4gIH1cclxuXHJcblxyXG5cclxuICBjb25zdCBjYWxjdWxhdGVUb3RhbCA9ICgpID0+IHtcclxuICAgIGlmICghc2VsZWN0ZWRQbGFuKSByZXR1cm4gMDtcclxuICAgIGxldCB0b3RhbCA9IHNlbGVjdGVkUGxhbi5wcmljZTtcclxuXHJcbiAgICAvLyBBZGRpdGlvbmFsIElQcyBjb3N0XHJcbiAgICB0b3RhbCArPSBhZGRpdGlvbmFsSVBzICogMTU7IC8vIDE1IE1BRCBwZXIgYWRkaXRpb25hbCBJUFxyXG5cclxuICAgIC8vIEJhY2t1cCBjb3N0IChvbmx5IGlmIG5vdCBhdXRvIGJhY2t1cCBwbGFuKVxyXG4gICAgaWYgKCFpc0F1dG9CYWNrdXAgJiYgYmFja3VwRW5hYmxlZCkge1xyXG4gICAgICB0b3RhbCArPSAyMDsgLy8gMjAgTUFEIGZvciBiYWNrdXBcclxuICAgIH1cclxuXHJcbiAgICAvLyBBdXRvIEJhY2t1cCBjb3N0IChDb250YWJvIHN0eWxlKVxyXG4gICAgaWYgKGF1dG9CYWNrdXBPcHRpb24gPT09IFwiYXV0b1wiKSB7XHJcbiAgICAgIHRvdGFsICs9IDE4OyAvLyDigqwxLjc5IOKJiCAxOCBNQUQgcGVyIG1vbnRoXHJcbiAgICB9XHJcblxyXG4gICAgLy8gUHJpdmF0ZSBOZXR3b3JraW5nIGNvc3RcclxuICAgIGlmIChwcml2YXRlTmV0d29ya2luZyA9PT0gXCJlbmFibGVkXCIpIHtcclxuICAgICAgdG90YWwgKz0gMjg7IC8vIDI4IE1BRCBwZXIgbW9udGhcclxuICAgIH1cclxuXHJcbiAgICAvLyBJUHY0IGFkZGl0aW9uYWwgYWRkcmVzc2VzIGNvc3RcclxuICAgIGlmIChpcHY0QWRkcmVzc2VzID09PSAyKSB7XHJcbiAgICAgIHRvdGFsICs9IDQyOyAvLyA0MiBNQUQgcGVyIGFkZGl0aW9uYWwgSVBcclxuICAgIH1cclxuXHJcbiAgICAvLyBPYmplY3QgU3RvcmFnZSBjb3N0XHJcbiAgICBjb25zdCBvYmplY3RTdG9yYWdlQ29zdHMgPSB7XHJcbiAgICAgIFwiMjUwZ2JcIjogNTAsXHJcbiAgICAgIFwiNTAwZ2JcIjogOTUsXHJcbiAgICAgIFwiNzUwZ2JcIjogMTQwLFxyXG4gICAgICBcIjF0YlwiOiAxODBcclxuICAgIH07XHJcbiAgICBpZiAob2JqZWN0U3RvcmFnZSAhPT0gXCJub25lXCIgJiYgb2JqZWN0U3RvcmFnZUNvc3RzW29iamVjdFN0b3JhZ2VdKSB7XHJcbiAgICAgIHRvdGFsICs9IG9iamVjdFN0b3JhZ2VDb3N0c1tvYmplY3RTdG9yYWdlXTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBTZXJ2ZXIgTWFuYWdlbWVudCBjb3N0XHJcbiAgICBpZiAoc2VydmVyTWFuYWdlbWVudCA9PT0gXCJtYW5hZ2VkXCIpIHtcclxuICAgICAgdG90YWwgKz0gMTM0MDsgLy8gMTM0MCBNQUQgcGVyIG1vbnRoIGZvciBtYW5hZ2VkXHJcbiAgICB9XHJcblxyXG4gICAgLy8gTW9uaXRvcmluZyBjb3N0XHJcbiAgICBpZiAobW9uaXRvcmluZyA9PT0gXCJmdWxsXCIpIHtcclxuICAgICAgdG90YWwgKz0gMTQwOyAvLyAxNDAgTUFEIHBlciBtb250aCBmb3IgZnVsbCBtb25pdG9yaW5nXHJcbiAgICB9XHJcblxyXG4gICAgLy8gU1NMIGNvc3QgKG9uZS10aW1lIGNoYXJnZXMsIGJ1dCB3ZSdsbCBhZGQgbW9udGhseSBlcXVpdmFsZW50KVxyXG4gICAgY29uc3Qgc3NsQ29zdHMgPSB7XHJcbiAgICAgIFwiYmFzaWNcIjogNzYsIC8vIDkxNCBNQUQgLyAxMiBtb250aHMg4omIIDc2IE1BRCBwZXIgbW9udGhcclxuICAgICAgXCJ3aWxkY2FyZFwiOiAyMjggLy8gMjc0MCBNQUQgLyAxMiBtb250aHMg4omIIDIyOCBNQUQgcGVyIG1vbnRoXHJcbiAgICB9O1xyXG4gICAgaWYgKHNzbCAhPT0gXCJub25lXCIgJiYgc3NsQ29zdHNbc3NsXSkge1xyXG4gICAgICB0b3RhbCArPSBzc2xDb3N0c1tzc2xdO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEFwcGx5IHF1YW50aXR5XHJcbiAgICB0b3RhbCAqPSBxdWFudGl0eTtcclxuXHJcbiAgICAvLyBQZXJpb2QgbXVsdGlwbGllclxyXG4gICAgY29uc3QgbXVsdGlwbGllcnMgPSB7XHJcbiAgICAgIG1vbnRobHk6IDEsXHJcbiAgICAgICc2bW9udGhzJzogNiAqIDAuOTcsIC8vIDMlIGRpc2NvdW50XHJcbiAgICAgIGFubnVhbGx5OiAxMiAqIDAuOTAgICAvLyAxMCUgZGlzY291bnRcclxuICAgIH07XHJcblxyXG4gICAgcmV0dXJuIHRvdGFsICogbXVsdGlwbGllcnNbc2VsZWN0ZWRQZXJpb2RdO1xyXG4gIH07XHJcblxyXG5cclxuXHJcbiAgLy8gTG9hZGluZyBzdGF0ZVxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTUwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cclxuICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgTG9hZGluZyBWUFMgY29uZmlndXJhdGlvbi4uLlxyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvLyBFcnJvciBzdGF0ZVxyXG4gIGlmIChlcnJvcikge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNlwiIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgIEVycm9yIGxvYWRpbmcgVlBTIGNvbmZpZ3VyYXRpb25cclxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxyXG4gICAgICAgICAgICB7ZXJyb3J9XHJcbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKX1cclxuICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgUmV0cnlcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxyXG4gICAgICB7LyogSGVhZGVyIC0gTW9iaWxlIE9wdGltaXplZCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctc20gYm9yZGVyLWJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LTMgc206cHktNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGl0ZW1zLXN0YXJ0IHNtOml0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTMgc206Z2FwLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBzbTpnYXAtNCB3LWZ1bGwgc206dy1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cuaGlzdG9yeS5iYWNrKCl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS0zMDAgdGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIGZsZXgtc2hyaW5rLTBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxBcnJvd0xlZnRJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgc206bXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4gc206aW5saW5lXCI+e3QoJ2JhY2snKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy0wIGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg0XCIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LTJ4bCB0ZXh0LWdyYXktOTAwIGZvbnQtYm9sZCB0cnVuY2F0ZVwiPlxyXG4gICAgICAgICAgICAgICAgICB7dCgncGFnZV90aXRsZScpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCB0cnVuY2F0ZVwiPlxyXG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRQbGFuPy5uYW1lIHx8ICdMb2FkaW5nLi4uJ31cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHNtOnRleHQtcmlnaHQgdy1mdWxsIHNtOnctYXV0byBmbGV4LXNocmluay0wXCI+XHJcbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57dCgncHJpY2VfZnJvbScpfTwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDNcIiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHNtOnRleHQtMnhsIHRleHQtYmx1ZS02MDAgZm9udC1ib2xkXCI+XHJcbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRQbGFuPy5wcmljZSB8fCAwfSBNQUR7dCgncGVyX21vbnRoJyl9XHJcbiAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktNiBzbTpweS04XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC02IGxnOmdhcC04XCI+XHJcbiAgICAgICAgICB7LyogQ29uZmlndXJhdGlvbiBQYW5lbCAtIE1vYmlsZSBPcHRpbWl6ZWQgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTIgc3BhY2UteS02IHNtOnNwYWNlLXktOFwiPlxyXG5cclxuICAgICAgICAgICAgey8qIFBsYW4gU3VtbWFyeSAtIE1vYmlsZSBPcHRpbWl6ZWQgKi99XHJcbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInNoYWRvdy1sZ1wiPlxyXG4gICAgICAgICAgICAgIDxDYXJkQm9keSBjbGFzc05hbWU9XCJwLTQgc206cC02XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHNtOmdhcC00IG1iLTQgc206bWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgc206dy0xMiBoLTEwIHNtOmgtMTIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8U2VydmVySWNvbiBjbGFzc05hbWU9XCJ3LTUgc206dy02IGgtNSBzbTpoLTYgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LTAgZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg1XCIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIHRleHQtZ3JheS05MDAgZm9udC1ib2xkIHRydW5jYXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRQbGFuPy5uYW1lIHx8ICdMb2FkaW5nLi4uJ31cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAge2lzQXV0b0JhY2t1cCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFNoaWVsZEljb24gY2xhc3NOYW1lPVwidy0zIHNtOnctNCBoLTMgc206aC00IHRleHQtZ3JlZW4tNjAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtXCI+QXV0byBCYWNrdXAgSW5jbHVzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgc206Z3JpZC1jb2xzLTQgZ2FwLTMgc206Z2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTIgc206cC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDcHVJY29uIGNsYXNzTmFtZT1cInctNSBzbTp3LTYgaC01IHNtOmgtNiB0ZXh0LWJsdWUtNjAwIG14LWF1dG8gbWItMSBzbTptYi0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3QoJ3ZjcHVfY29yZXMnKX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkUGxhbj8uY29yZXMgfHwgMH08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC0yIHNtOnAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNSBzbTp3LTYgaC01IHNtOmgtNiBiZy1ibHVlLTYwMCByb3VuZGVkIG14LWF1dG8gbWItMSBzbTptYi0yXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTYwMFwiPnt0KCdyYW0nKX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkUGxhbj8ucmFtIHx8ICcwIEdCJ308L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC0yIHNtOnAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICA8SGFyZERyaXZlSWNvbiBjbGFzc05hbWU9XCJ3LTUgc206dy02IGgtNSBzbTpoLTYgdGV4dC1ibHVlLTYwMCBteC1hdXRvIG1iLTEgc206bWItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTYwMFwiPnt0KCdzdG9yYWdlJyl9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LWJvbGQgdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZFBsYW4/LnN0b3JhZ2UgfHwgJzAgR0InfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTIgc206cC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxHbG9iZUljb24gY2xhc3NOYW1lPVwidy01IHNtOnctNiBoLTUgc206aC02IHRleHQtYmx1ZS02MDAgbXgtYXV0byBtYi0xIHNtOm1iLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj57dCgndHJhZmZpYycpfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRQbGFuPy50cmFmZmljIHx8ICcwIFRCJ308L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICB7LyogQmlsbGluZyBQZXJpb2QgLSBNb2JpbGUgT3B0aW1pemVkICovfVxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC00IHNtOnAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIHRleHQtZ3JheS05MDAgZm9udC1ib2xkIG1iLTMgc206bWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAxLiB7dCgnYmlsbGluZ19wZXJpb2QnKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS02MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICB7dCgnYmlsbGluZ19wZXJpb2RfZGVzYycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgICAgICAgICAge1tcclxuICAgICAgICAgICAgICAgICAgICB7IGlkOiAnbW9udGhseScsIGxhYmVsOiAnMSBtb2lzJywgZGlzY291bnQ6ICcnLCBwcmljZTogc2VsZWN0ZWRQbGFuPy5wcmljZSB8fCAwIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgeyBpZDogJzZtb250aHMnLCBsYWJlbDogJzYgbW9pcycsIGRpc2NvdW50OiAnMyUgZGUgcsOpZHVjdGlvbicsIHByaWNlOiBNYXRoLnJvdW5kKChzZWxlY3RlZFBsYW4/LnByaWNlIHx8IDApICogNiAqIDAuOTcpIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgeyBpZDogJ2FubnVhbGx5JywgbGFiZWw6ICcxMiBtb2lzJywgZGlzY291bnQ6ICcxMCUgZGUgcsOpZHVjdGlvbicsIHByaWNlOiBNYXRoLnJvdW5kKChzZWxlY3RlZFBsYW4/LnByaWNlIHx8IDApICogMTIgKiAwLjkwKSB9XHJcbiAgICAgICAgICAgICAgICAgIF0ubWFwKChwZXJpb2QpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e3BlcmlvZC5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkUGVyaW9kKHBlcmlvZC5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgc206cC00IGJvcmRlci0yIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUGVyaW9kID09PSBwZXJpb2QuaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS02MDAgYmctYmx1ZS01MCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBzbTpnYXAtMyBtaW4tdy0wIGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy00IGgtNCByb3VuZGVkLWZ1bGwgYm9yZGVyLTIgZmxleC1zaHJpbmstMCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRQZXJpb2QgPT09IHBlcmlvZC5pZCA/ICdib3JkZXItYmx1ZS02MDAgYmctYmx1ZS02MDAnIDogJ2JvcmRlci1ncmF5LTMwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRQZXJpb2QgPT09IHBlcmlvZC5pZCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy13aGl0ZSByb3VuZGVkLWZ1bGwgbXgtYXV0byBtdC0wLjVcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy0wIGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+e3BlcmlvZC5sYWJlbH08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwZXJpb2QuZGlzY291bnQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbSB0ZXh0LWdyZWVuLTYwMCBmb250LW1lZGl1bVwiPntwZXJpb2QuZGlzY291bnR9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IGZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+e3BlcmlvZC5wcmljZX0gTUFEPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3BlcmlvZC5pZCA9PT0gJ21vbnRobHknID8gJy9tb2lzJyA6IHBlcmlvZC5pZCA9PT0gJzZtb250aHMnID8gJy82IG1vaXMnIDogJy9hbid9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgey8qIE9wZXJhdGluZyBTeXN0ZW0gU2VsZWN0aW9uIC0gTW9iaWxlIE9wdGltaXplZCAqL31cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LWxnXCI+XHJcbiAgICAgICAgICAgICAgPENhcmRCb2R5IGNsYXNzTmFtZT1cInAtNCBzbTpwLTZcIj5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNlwiIGNsYXNzTmFtZT1cInRleHQtbGcgc206dGV4dC14bCB0ZXh0LWdyYXktOTAwIGZvbnQtYm9sZCBtYi0zIHNtOm1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgMi4ge3QoJ2Nob29zZV9vcycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0KCdjaG9vc2Vfb3NfZGVzYycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAge29zSW1hZ2VzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+8J+UhCBDaGFyZ2VtZW50IGRlcyBzeXN0w6htZXMgZCdleHBsb2l0YXRpb24uLi48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+KHtvcGVyYXRpbmdTeXN0ZW1zLmxlbmd0aH0gT1MgZGlzcG9uaWJsZXMgZW4gZmFsbGJhY2spPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIvCflIQgRm9yY2UgZmV0Y2hpbmcgT1MgaW1hZ2VzLi4uXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdnBzU2VydmljZS5nZXRJbWFnZXMoJ2NvbnRhYm8nKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIuKchSBGb3JjZSBmZXRjaCByZXN1bHQ6XCIsIHJlc3BvbnNlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi4p2MIEZvcmNlIGZldGNoIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHB4LTQgcHktMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgdGV4dC1zbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAg8J+UhCBGb3JjZXIgbGUgY2hhcmdlbWVudCBkZXMgT1NcclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIHRleHQtc21cIj7inIUge29zSW1hZ2VzLmxlbmd0aH0gc3lzdMOobWVzIGQnZXhwbG9pdGF0aW9uIGNoYXJnw6lzIGRlcHVpcyBsJ0FQSTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAge29wZXJhdGluZ1N5c3RlbXMubWFwKChvcykgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgIGtleT17b3MuaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZE9TKG9zLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIHAtMyBzbTpwLTQgYm9yZGVyLTIgcm91bmRlZC1sZyBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRPUyA9PT0gb3MuaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS02MDAgYmctYmx1ZS01MCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBzbTpnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8b3MuaWNvbiBjbGFzc05hbWU9XCJ3LTggaC04IHNtOnctMTAgc206aC0xMCBmbGV4LXNocmluay0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy0wIGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMCBicmVhay13b3Jkc1wiPntvcy5uYW1lfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge29zLnR5cGUgPT09ICdsaW51eCcgPyAnTGludXggRGlzdHJpYnV0aW9uJyA6ICdXaW5kb3dzIFNlcnZlcid9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRPUyA9PT0gb3MuaWQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0ljb24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWJsdWUtNjAwIGZsZXgtc2hyaW5rLTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgey8qIExvY2F0aW9uIFNlbGVjdGlvbiAtIE1vYmlsZSBPcHRpbWl6ZWQgKi99XHJcbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInNoYWRvdy1sZ1wiPlxyXG4gICAgICAgICAgICAgIDxDYXJkQm9keSBjbGFzc05hbWU9XCJwLTQgc206cC02XCI+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHNtOnRleHQteGwgdGV4dC1ncmF5LTkwMCBmb250LWJvbGQgbWItMyBzbTptYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDMuIHt0KCdjaG9vc2VfbG9jYXRpb24nKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS02MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICB7dCgnY2hvb3NlX2xvY2F0aW9uX2Rlc2MnKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgIHtyZWdpb25zLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+8J+UhCBDaGFyZ2VtZW50IGRlcyBlbXBsYWNlbWVudHMuLi48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+KHtsb2NhdGlvbnMubGVuZ3RofSBlbXBsYWNlbWVudHMgZGlzcG9uaWJsZXMgZW4gZmFsbGJhY2spPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIvCflIQgRm9yY2UgZmV0Y2hpbmcgcmVnaW9ucy4uLlwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHZwc1NlcnZpY2UuZ2V0UmVnaW9ucygnY29udGFibycpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIEZvcmNlIGZldGNoIHJlZ2lvbnMgcmVzdWx0OlwiLCByZXNwb25zZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBGb3JjZSBmZXRjaCByZWdpb25zIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHB4LTQgcHktMiBiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSByb3VuZGVkIHRleHQtc21cIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIPCflIQgRm9yY2VyIGxlIGNoYXJnZW1lbnQgZGVzIHLDqWdpb25zXHJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCB0ZXh0LXNtXCI+4pyFIHtyZWdpb25zLmxlbmd0aH0gZW1wbGFjZW1lbnRzIGNoYXJnw6lzIGRlcHVpcyBsJ0FQSTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAge2xvY2F0aW9ucy5tYXAoKGxvY2F0aW9uKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtsb2NhdGlvbi5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkTG9jYXRpb24obG9jYXRpb24uaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIHNtOnAtNCBib3JkZXItMiByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZExvY2F0aW9uID09PSBsb2NhdGlvbi5pZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTYwMCBiZy1ibHVlLTUwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHNtOmdhcC0zIG1pbi13LTAgZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bCBzbTp0ZXh0LTJ4bCBmbGV4LXNocmluay0wXCI+e2xvY2F0aW9uLmlkfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LTAgZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS05MDAgdHJ1bmNhdGVcIj57bG9jYXRpb24ubmFtZX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZExvY2F0aW9uID09PSBsb2NhdGlvbi5pZCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrSWNvbiBjbGFzc05hbWU9XCJ3LTQgc206dy01IGgtNCBzbTpoLTUgdGV4dC1ibHVlLTYwMCBmbGV4LXNocmluay0wXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgIHsvKiA0LiBEYXRhIFByb3RlY3Rpb24gd2l0aCBBdXRvIEJhY2t1cCAtIEltcHJvdmVkIFN0eWxlICovfVxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC00IHNtOnAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIHRleHQtZ3JheS05MDAgZm9udC1ib2xkIG1iLTMgc206bWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICA0LiBEYXRhIFByb3RlY3Rpb24gd2l0aCBBdXRvIEJhY2t1cFxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XHJcbiAgICAgICAgICAgICAgICAgIHsvKiBBdXRvIEJhY2t1cCBPcHRpb24gKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBdXRvQmFja3VwT3B0aW9uKFwiYXV0b1wiKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBwLTYgYm9yZGVyLTIgcm91bmRlZC14bCBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGF1dG9CYWNrdXBPcHRpb24gPT09IFwiYXV0b1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCBiZy1ibHVlLTUwIHNoYWRvdy1tZCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ibHVlLTMwMCBob3ZlcjpzaGFkb3ctc20nXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7YXV0b0JhY2t1cE9wdGlvbiA9PT0gXCJhdXRvXCIgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTE2LjcwNyA1LjI5M2ExIDEgMCAwMTAgMS40MTRsLTggOGExIDEgMCAwMS0xLjQxNCAwbC00LTRhMSAxIDAgMDExLjQxNC0xLjQxNEw4IDEyLjU4Nmw3LjI5My03LjI5M2ExIDEgMCAwMTEuNDE0IDB6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIHRleHQteHMgZm9udC1tZWRpdW0gcHgtMyBweS0xIHJvdW5kZWQtZnVsbCBpbmxpbmUtYmxvY2sgbWItM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBOb3RyZSBSZWNvbW1hbmRhdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXhsIHRleHQtZ3JheS05MDAgbWItMlwiPkF1dG8gQmFja3VwPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgZm9udC1ib2xkIHRleHQtbGcgbWItM1wiPjE4IE1BRC9tb2lzPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItMiBmb250LW1lZGl1bVwiPlNldCBpdCBhbmQgZm9yZ2V0IGl0LjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc20gbWItNFwiPkRhdGEgc2VjdXJpdHkgd2l0aCBubyBlZmZvcnQ8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC00IHNwYWNlLXktMyB0ZXh0LXNtIGJvcmRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Nb2RlPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPmF1dG9tYXRlZDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkZyZXF1ZW5jeTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5kYWlseTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlJlY292ZXJ5PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPjEtQ2xpY2sgUmVjb3Zlcnk8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5CYWNrdXAgUmV0ZW50aW9uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPjEwIGRheXM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIE5vbmUgT3B0aW9uICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QXV0b0JhY2t1cE9wdGlvbihcIm5vbmVcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgcC02IGJvcmRlci0yIHJvdW5kZWQteGwgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhdXRvQmFja3VwT3B0aW9uID09PSBcIm5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MCBzaGFkb3ctbWQnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItYmx1ZS0zMDAgaG92ZXI6c2hhZG93LXNtJ1xyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge2F1dG9CYWNrdXBPcHRpb24gPT09IFwibm9uZVwiICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xNi43MDcgNS4yOTNhMSAxIDAgMDEwIDEuNDE0bC04IDhhMSAxIDAgMDEtMS40MTQgMGwtNC00YTEgMSAwIDAxMS40MTQtMS40MTRMOCAxMi41ODZsNy4yOTMtNy4yOTNhMSAxIDAgMDExLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LWJvbGQgdGV4dC14bCB0ZXh0LWdyYXktOTAwIG1iLTIgbXQtOFwiPk5vbmU8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgZm9udC1ib2xkIHRleHQtbGcgbWItNlwiPkZyZWU8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC00IHNwYWNlLXktMyB0ZXh0LXNtIGJvcmRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Nb2RlPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPm1hbnVhbDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkZyZXF1ZW5jeTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5vbiBkZW1hbmQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5SZWNvdmVyeTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5tYW51YWw8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5CYWNrdXAgUmV0ZW50aW9uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPi08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgey8qIDUuIE5ldHdvcmtpbmcgLSBDb250YWJvIFN0eWxlICovfVxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC00IHNtOnAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIHRleHQtZ3JheS05MDAgZm9udC1ib2xkIG1iLTMgc206bWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICA1LiBOZXR3b3JraW5nXHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcblxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgICAgey8qIFByaXZhdGUgTmV0d29ya2luZyAqL31cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHktMiBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+UHJpdmF0ZSBOZXR3b3JraW5nPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ByaXZhdGVOZXR3b3JraW5nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByaXZhdGVOZXR3b3JraW5nKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIHB4LTMgcHktMiBwci04IG1pbi13LVsyNDBweF0gYXBwZWFyYW5jZS1ub25lIGJnLXdoaXRlIHRleHQtc20gc206dGV4dC1iYXNlXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5vbmVcIj5ObyBQcml2YXRlIE5ldHdvcmtpbmc8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImVuYWJsZWRcIj5Qcml2YXRlIE5ldHdvcmtpbmcgRW5hYmxlZDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LVs2MHB4XSB0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtwcml2YXRlTmV0d29ya2luZyA9PT0gXCJlbmFibGVkXCIgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj4yOCBNQUQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+RnJlZTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBCYW5kd2lkdGggKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB5LTIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPkJhbmR3aWR0aDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPjMyIFRCIE91dCArIFVubGltaXRlZCBJbjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1wdXJwbGUtNjAwXCI+MjAwIE1iaXQvcyBDb25uZWN0aW9uPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIElQdjQgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB5LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+SVB2NDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpcHY0QWRkcmVzc2VzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldElwdjRBZGRyZXNzZXMocGFyc2VJbnQoZS50YXJnZXQudmFsdWUpKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIHB4LTMgcHktMiBwci04IG1pbi13LVsyNDBweF0gYXBwZWFyYW5jZS1ub25lIGJnLXdoaXRlIHRleHQtc20gc206dGV4dC1iYXNlXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17MX0+MSBJUCBBZGRyZXNzPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezJ9PjEgSVAgQWRkcmVzcyArIDEgQWRkaXRpb25hbCBJUDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LVs2MHB4XSB0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtpcHY0QWRkcmVzc2VzID09PSAyID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+NDIgTUFEPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPkZyZWU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICB7LyogNi4gQWRkLU9ucyAtIENvbnRhYm8gU3R5bGUgKi99XHJcbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInNoYWRvdy1sZ1wiPlxyXG4gICAgICAgICAgICAgIDxDYXJkQm9keSBjbGFzc05hbWU9XCJwLTQgc206cC02XCI+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHNtOnRleHQteGwgdGV4dC1ncmF5LTkwMCBmb250LWJvbGQgbWItMyBzbTptYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDYuIEFkZC1PbnNcclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgICB7LyogT2JqZWN0IFN0b3JhZ2UgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB5LTIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPk9iamVjdCBTdG9yYWdlPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29iamVjdFN0b3JhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0T2JqZWN0U3RvcmFnZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZCBweC0zIHB5LTIgcHItOCBtaW4tdy1bMjQwcHhdIGFwcGVhcmFuY2Utbm9uZSBiZy13aGl0ZSB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJub25lXCI+Tm9uZTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMjUwZ2JcIj4yNTAgR0IgT2JqZWN0IFN0b3JhZ2U8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjUwMGdiXCI+NTAwIEdCIE9iamVjdCBTdG9yYWdlPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCI3NTBnYlwiPjc1MCBHQiBPYmplY3QgU3RvcmFnZTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMXRiXCI+MSBUQiBPYmplY3QgU3RvcmFnZTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LVs4MHB4XSB0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtvYmplY3RTdG9yYWdlID09PSBcIjI1MGdiXCIgJiYgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj41MCBNQUQ8L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7b2JqZWN0U3RvcmFnZSA9PT0gXCI1MDBnYlwiICYmIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+OTUgTUFEPC9zcGFuPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAge29iamVjdFN0b3JhZ2UgPT09IFwiNzUwZ2JcIiAmJiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPjE0MCBNQUQ8L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7b2JqZWN0U3RvcmFnZSA9PT0gXCIxdGJcIiAmJiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPjE4MCBNQUQ8L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7b2JqZWN0U3RvcmFnZSA9PT0gXCJub25lXCIgJiYgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+RnJlZTwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogU2VydmVyIE1hbmFnZW1lbnQgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB5LTIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPlNlcnZlciBNYW5hZ2VtZW50PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlcnZlck1hbmFnZW1lbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VydmVyTWFuYWdlbWVudChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZCBweC0zIHB5LTIgcHItOCBtaW4tdy1bMjQwcHhdIGFwcGVhcmFuY2Utbm9uZSBiZy13aGl0ZSB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJ1bm1hbmFnZWRcIj5Vbm1hbmFnZWQ8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1hbmFnZWRcIj5NYW5hZ2VkPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctWzgwcHhdIHRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3NlcnZlck1hbmFnZW1lbnQgPT09IFwidW5tYW5hZ2VkXCIgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+RnJlZTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPjEzNDAgTUFEPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIE1vbml0b3JpbmcgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB5LTIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPk1vbml0b3Jpbmc8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bW9uaXRvcmluZ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRNb25pdG9yaW5nKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIHB4LTMgcHktMiBwci04IG1pbi13LVsyNDBweF0gYXBwZWFyYW5jZS1ub25lIGJnLXdoaXRlIHRleHQtc20gc206dGV4dC1iYXNlXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5vbmVcIj5Ob25lPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJmdWxsXCI+RnVsbCBNb25pdG9yaW5nPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctWzgwcHhdIHRleHQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21vbml0b3JpbmcgPT09IFwiZnVsbFwiID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+MTQwIE1BRDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj5GcmVlPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIFNTTCAqL31cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS05MDBcIj5TU0w8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c3NsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNzbChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZCBweC0zIHB5LTIgcHItOCBtaW4tdy1bMjQwcHhdIGFwcGVhcmFuY2Utbm9uZSBiZy13aGl0ZSB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJub25lXCI+Tm9uZTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYmFzaWNcIj5TU0wgY2VydGlmaWNhdGU8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIndpbGRjYXJkXCI+U1NMIGNlcnRpZmljYXRlICh3aWxkY2FyZCk8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy1bODBweF0gdGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7c3NsID09PSBcImJhc2ljXCIgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPjkxNCBNQUQ8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5PbmUgb2ZmIGNoYXJnZTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7c3NsID09PSBcIndpbGRjYXJkXCIgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPjI3NDAgTUFEPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+T25lIG9mZiBjaGFyZ2U8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAge3NzbCA9PT0gXCJub25lXCIgJiYgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+RnJlZTwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICB7LyogTG9naW4gJiBQYXNzd29yZCBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC00IHNtOnAtNlwiPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCIgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIHRleHQtZ3JheS03MDAgZm9udC1zZW1pYm9sZCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgIDguIExvZ2luICYgcGFzc3dvcmQgZm9yIHlvdXIgc2VydmVyXHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcblxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgICAgey8qIFVzZXJuYW1lICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIGdhcC0yIHNtOmdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS05MDAgbWluLXctWzEwMHB4XVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgVXNlcm5hbWVcclxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgYmctZ3JheS01MCBweC0zIHB5LTIgcm91bmRlZCBib3JkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHJvb3RcclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIFBhc3N3b3JkICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtc3RhcnQgZ2FwLTIgc206Z2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwIG1pbi13LVsxMDBweF0gc206bXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBQYXNzd29yZFxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gey8qIEFkZCBnZW5lcmF0ZSBwYXNzd29yZCBsb2dpYyAqL319XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIHRleHQtc20gdW5kZXJsaW5lIG1iLTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgR2VuZXJhdGUgbmV3IHBhc3N3b3JkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPXtzaG93UGFzc3dvcmQgPyBcInRleHRcIiA6IFwicGFzc3dvcmRcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwYXNzd29yZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQYXNzd29yZENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgYSBzZWN1cmUgcGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGJvcmRlciByb3VuZGVkIHB4LTMgcHktMiBwci0xMiB0ZXh0LXNtIHNtOnRleHQtYmFzZSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXNzd29yZEVycm9yID8gJ2JvcmRlci1yZWQtNTAwIGJnLXJlZC01MCcgOiAnYm9yZGVyLWdyYXktMzAwIGJnLXdoaXRlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQYXNzd29yZCghc2hvd1Bhc3N3b3JkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93UGFzc3dvcmQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFeWVPZmZJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZUljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgIHtwYXNzd29yZEVycm9yICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1yZWQtNjAwIHRleHQtc20gbWwtMCBzbTptbC1bMTE2cHhdXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZC1mdWxsIGJnLXJlZC02MDAgdGV4dC13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXhzIGZvbnQtYm9sZFwiPiE8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFBsZWFzZSBlbnRlciBhIHZhbGlkIHBhc3N3b3JkXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIG1sLTAgc206bWwtWzExNnB4XSBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgSW4gb3JkZXIgdG8gdXNlIFNTSCBLZXlzIHlvdSBjYW4gYWRkIHRoZW0gaW4gdGhlIEN1c3RvbWVyIENvbnRyb2wgUGFuZWwgbGF0ZXIuIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBZb3VyIHBhc3N3b3JkIHdpbGwgbm90IGJlIHNlbnQgdmlhIGVtYWlsLiBCZSBzdXJlIHRvIHJlbWVtYmVyIGl0IGZvciBXaW5kb3dzIGFjY2Vzcy4gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIElmIHlvdSBmb3JnZXQgdGhlIHBhc3N3b3JkLCB5b3Ugd2lsbCBuZWVkIHRvIHJlaW5zdGFsbCB5b3VyIHNlcnZlci5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIE9yZGVyIFN1bW1hcnkgLSBGaXhlZCBQb3NpdGlvbiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOnN0aWNreSBsZzp0b3AtNCBsZzptYXgtaC1bY2FsYygxMDB2aC0ycmVtKV0gbGc6b3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LWxnIGgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgPENhcmRCb2R5IGNsYXNzTmFtZT1cInAtNCBzbTpwLTYgZmxleCBmbGV4LWNvbCBoLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNlwiIGNsYXNzTmFtZT1cInRleHQtbGcgc206dGV4dC14bCB0ZXh0LWdyYXktOTAwIGZvbnQtYm9sZCBtYi00IHNtOm1iLTYgZmxleC1zaHJpbmstMFwiPlxyXG4gICAgICAgICAgICAgICAgICB7dCgnb3JkZXJfc3VtbWFyeScpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIHNtOnNwYWNlLXktNCBtYi00IHNtOm1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctMCBmbGV4LTEgcHItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMFwiPntzZWxlY3RlZFBsYW4/Lm5hbWUgfHwgJ0xvYWRpbmcuLi4nfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbXQtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5RdWFudGl0w6k6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRRdWFudGl0eShNYXRoLm1heCgxLCBxdWFudGl0eSAtIDEpKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtxdWFudGl0eSA9PT0gMX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgcC0wIGJvcmRlci1ncmF5LTMwMCB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgZmxleC1zaHJpbmstMCBtaW4tdy0wXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy04IHRleHQtY2VudGVyIGZvbnQtbWVkaXVtIHRleHQteHNcIj57cXVhbnRpdHl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UXVhbnRpdHkoTWF0aC5taW4oMTAsIHF1YW50aXR5ICsgMSkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3F1YW50aXR5ID09PSAxMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgcC0wIGJvcmRlci1ncmF5LTMwMCB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgZmxleC1zaHJpbmstMCBtaW4tdy0wXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICArXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSBzbTp0ZXh0LWJhc2UgZmxleC1zaHJpbmstMFwiPnsoc2VsZWN0ZWRQbGFuPy5wcmljZSB8fCAwKSAqIHF1YW50aXR5fSBNQUQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAge2FkZGl0aW9uYWxJUHMgPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktNjAwIG1pbi13LTAgZmxleC0xIHByLTJcIj5JUHMgYWRkaXRpb25uZWxsZXMgKHthZGRpdGlvbmFsSVBzfSDDlyB7cXVhbnRpdHl9KTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlIGZsZXgtc2hyaW5rLTBcIj57YWRkaXRpb25hbElQcyAqIDE1ICogcXVhbnRpdHl9IE1BRDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgIHshaXNBdXRvQmFja3VwICYmIGJhY2t1cEVuYWJsZWQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS02MDAgbWluLXctMCBmbGV4LTEgcHItMlwiPlNhdXZlZ2FyZGUgYXV0b21hdGlxdWUgw5cge3F1YW50aXR5fTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlIGZsZXgtc2hyaW5rLTBcIj57MjAgKiBxdWFudGl0eX0gTUFEPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkUGVyaW9kICE9PSAnbW9udGhseScgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgdGV4dC1ncmVlbi02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIG1pbi13LTAgZmxleC0xIHByLTJcIj5Sw6lkdWN0aW9uICh7c2VsZWN0ZWRQZXJpb2QgPT09ICc2bW9udGhzJyA/ICczJScgOiAnMTAlJ30pPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgZmxleC1zaHJpbmstMFwiPi17TWF0aC5yb3VuZCgoc2VsZWN0ZWRQbGFuPy5wcmljZSB8fCAwKSAqIHF1YW50aXR5ICogKHNlbGVjdGVkUGVyaW9kID09PSAnNm1vbnRocycgPyA2ICogMC4wMyA6IDEyICogMC4xMCkpfSBNQUQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICA8aHIgY2xhc3NOYW1lPVwiYm9yZGVyLWdyYXktMjAwXCIgLz5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgdGV4dC1iYXNlIHNtOnRleHQtbGcgZm9udC1ib2xkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWluLXctMCBmbGV4LTEgcHItMlwiPnt0KCd0b3RhbCcpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGZsZXgtc2hyaW5rLTAgdGV4dC1yaWdodFwiPntNYXRoLnJvdW5kKGNhbGN1bGF0ZVRvdGFsKCkpfSBNQUQve3NlbGVjdGVkUGVyaW9kID09PSAnbW9udGhseScgPyAnbW9pcycgOiBzZWxlY3RlZFBlcmlvZCA9PT0gJzZtb250aHMnID8gJzYgbW9pcycgOiAnYW4nfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogQ29uZmlndXJhdGlvbiBTdW1tYXJ5IC0gTW9iaWxlIE9wdGltaXplZCAqL31cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHNtOm10LTYgcHQtMyBzbTpwdC00IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS02MDAgc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwXCI+PHN0cm9uZz57dCgnb3NfbGFiZWwnKX06PC9zdHJvbmc+Jm5ic3A7PHNwYW4gY2xhc3NOYW1lPVwiYnJlYWstYWxsXCI+e29wZXJhdGluZ1N5c3RlbXMuZmluZChvcyA9PiBvcy5pZCA9PT0gc2VsZWN0ZWRPUyk/Lm5hbWV9PC9zcGFuPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcFwiPjxzdHJvbmc+e3QoJ2xvY2F0aW9uX2xhYmVsJyl9Ojwvc3Ryb25nPiZuYnNwOzxzcGFuIGNsYXNzTmFtZT1cImJyZWFrLWFsbFwiPntsb2NhdGlvbnMuZmluZChsb2MgPT4gbG9jLmlkID09PSBzZWxlY3RlZExvY2F0aW9uKT8ubmFtZX08L3NwYW4+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwXCI+PHN0cm9uZz5Qw6lyaW9kZTo8L3N0cm9uZz4mbmJzcDs8c3BhbiBjbGFzc05hbWU9XCJicmVhay1hbGxcIj57c2VsZWN0ZWRQZXJpb2QgPT09ICdtb250aGx5JyA/ICcxIG1vaXMnIDogc2VsZWN0ZWRQZXJpb2QgPT09ICc2bW9udGhzJyA/ICc2IG1vaXMnIDogJzEyIG1vaXMnfTwvc3Bhbj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tYmx1ZS03MDAgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1ibHVlLTgwMCBtYi00IHB5LTMgc206cHktNCB0ZXh0LXNtIHNtOnRleHQtYmFzZSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkVG9DYXJ0fVxyXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17b3JkZXJMb2FkaW5nIHx8IGxvYWRpbmcgfHwgIXNlbGVjdGVkUGxhbn1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge29yZGVyTG9hZGluZyA/IFwiQWpvdXQgZW4gY291cnMuLi5cIiA6IFwiQWpvdXRlciBhdSBwYW5pZXJcIn1cclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiB0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTUwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNoaWVsZEljb24gY2xhc3NOYW1lPVwidy0zIHNtOnctNCBoLTMgc206aC00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5QYWllbWVudCBzw6ljdXJpc8OpPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiB0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwidy0zIHNtOnctNCBoLTMgc206aC00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Ew6lwbG9pZW1lbnQgZW4gNSBtaW51dGVzPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNlYXJjaFBhcmFtcyIsInVzZVJvdXRlciIsInVzZVRyYW5zbGF0aW9ucyIsIlR5cG9ncmFwaHkiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZEJvZHkiLCJ2cHNTZXJ2aWNlIiwiY2FydFNlcnZpY2UiLCJwYWNrYWdlU2VydmljZSIsInVzZUF1dGgiLCJ0b2FzdCIsIlNlcnZlckljb24iLCJDcHVJY29uIiwiSGFyZERyaXZlSWNvbiIsIkdsb2JlSWNvbiIsIlNoaWVsZEljb24iLCJDbG9ja0ljb24iLCJDaGVja0ljb24iLCJBcnJvd0xlZnRJY29uIiwiTW9uaXRvckljb24iLCJUZXJtaW5hbEljb24iLCJFeWVJY29uIiwiRXllT2ZmSWNvbiIsIktleUljb24iLCJVc2VySWNvbiIsIlVidW50dUljb24iLCJjbGFzc05hbWUiLCJkaXYiLCJzdmciLCJ2aWV3Qm94IiwiZmlsbCIsInBhdGgiLCJkIiwiQ2VudE9TSWNvbiIsIkRlYmlhbkljb24iLCJXaW5kb3dzSWNvbiIsIkNvbmZpZ3VyZVZQU1BhZ2UiLCJvcGVyYXRpbmdTeXN0ZW1zIiwibG9jYXRpb25zIiwic2VhcmNoUGFyYW1zIiwicm91dGVyIiwidCIsInNldENhcnRDb3VudCIsInZwc1BsYW5zIiwic2V0VnBzUGxhbnMiLCJvc0ltYWdlcyIsInNldE9zSW1hZ2VzIiwicmVnaW9ucyIsInNldFJlZ2lvbnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJvcmRlckxvYWRpbmciLCJzZXRPcmRlckxvYWRpbmciLCJwbGFuSWQiLCJnZXQiLCJhdXRvQmFja3VwIiwic2VsZWN0ZWRQbGFuIiwic2V0U2VsZWN0ZWRQbGFuIiwiaXNBdXRvQmFja3VwIiwic2V0SXNBdXRvQmFja3VwIiwicGFyc2VTcGVjaWZpY2F0aW9ucyIsInNwZWNpZmljYXRpb25zIiwiZGVzY3JpcHRpb24iLCJjb3JlcyIsInJhbSIsInN0b3JhZ2UiLCJ0cmFmZmljIiwiQXJyYXkiLCJpc0FycmF5IiwiZm9yRWFjaCIsInNwZWMiLCJ2YWx1ZSIsImxvd2VyVmFsdWUiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiY3B1TWF0Y2giLCJtYXRjaCIsInBhcnNlSW50IiwicmFtTWF0Y2giLCJzdG9yYWdlTWF0Y2giLCJzdG9yYWdlVHlwZSIsInRyaW0iLCJ0cmFmZmljTWF0Y2giLCJ0b1VwcGVyQ2FzZSIsImRlc2NMb3dlciIsImZldGNoVlBTUGFja2FnZXMiLCJyZXNwb25zZSIsImdldFBhY2thZ2VzIiwiY29uc29sZSIsImxvZyIsInZwc1BhY2thZ2VzIiwiZGF0YSIsInBhY2thZ2VzIiwidHJhbnNmb3JtZWRQbGFucyIsIm1hcCIsInBrZyIsInNwZWNzIiwiaWQiLCJfaWQiLCJuYW1lIiwicHJpY2UiLCJsZW5ndGgiLCJmb3VuZFBsYW4iLCJmaW5kIiwicGxhbiIsInRvU3RyaW5nIiwicCIsImZldGNoT1NJbWFnZXMiLCJnZXRJbWFnZXMiLCJpbWFnZXMiLCJ0cmFuc2Zvcm1lZEltYWdlcyIsImltZyIsImltYWdlSWQiLCJ0eXBlIiwib3NUeXBlIiwidmVyc2lvbiIsInByb3ZpZGVyIiwiZGVmYXVsdE9TIiwic2V0U2VsZWN0ZWRPUyIsIm1lc3NhZ2UiLCJzdGF0aWNPc0ltYWdlcyIsImZldGNoUmVnaW9ucyIsImdldFJlZ2lvbnMiLCJ0cmFuc2Zvcm1lZFJlZ2lvbnMiLCJyZWdpb24iLCJyZWdpb25TbHVnIiwicmVnaW9uTmFtZSIsImRlZmF1bHRSZWdpb24iLCJzZXRTZWxlY3RlZExvY2F0aW9uIiwic3RhdGljUmVnaW9ucyIsImNvdW50cnkiLCJjaXR5Iiwic2VsZWN0ZWRPUyIsInNlbGVjdGVkTG9jYXRpb24iLCJzZWxlY3RlZFBlcmlvZCIsInNldFNlbGVjdGVkUGVyaW9kIiwiYWRkaXRpb25hbElQcyIsInNldEFkZGl0aW9uYWxJUHMiLCJiYWNrdXBFbmFibGVkIiwic2V0QmFja3VwRW5hYmxlZCIsInF1YW50aXR5Iiwic2V0UXVhbnRpdHkiLCJhdXRvQmFja3VwT3B0aW9uIiwic2V0QXV0b0JhY2t1cE9wdGlvbiIsInByaXZhdGVOZXR3b3JraW5nIiwic2V0UHJpdmF0ZU5ldHdvcmtpbmciLCJpcHY0QWRkcmVzc2VzIiwic2V0SXB2NEFkZHJlc3NlcyIsIm9iamVjdFN0b3JhZ2UiLCJzZXRPYmplY3RTdG9yYWdlIiwic2VydmVyTWFuYWdlbWVudCIsInNldFNlcnZlck1hbmFnZW1lbnQiLCJtb25pdG9yaW5nIiwic2V0TW9uaXRvcmluZyIsInNzbCIsInNldFNzbCIsInVzZXJuYW1lIiwic2V0VXNlcm5hbWUiLCJwYXNzd29yZCIsInNldFBhc3N3b3JkIiwic2hvd1Bhc3N3b3JkIiwic2V0U2hvd1Bhc3N3b3JkIiwicGFzc3dvcmRFcnJvciIsInNldFBhc3N3b3JkRXJyb3IiLCJ2YWxpZGF0ZVBhc3N3b3JkIiwicHdkIiwidGVzdCIsImdlbmVyYXRlU2VjdXJlUGFzc3dvcmQiLCJsb3dlcmNhc2UiLCJ1cHBlcmNhc2UiLCJudW1iZXJzIiwic3ltYm9scyIsImFsbENoYXJzIiwibmV3UGFzc3dvcmQiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJpIiwic3BsaXQiLCJzb3J0Iiwiam9pbiIsImhhbmRsZVBhc3N3b3JkQ2hhbmdlIiwiZSIsInRhcmdldCIsImhhbmRsZUFkZFRvQ2FydCIsInBhc3N3b3JkVmFsaWRhdGlvbkVycm9yIiwicGFja2FnZUlkIiwiY29udGFib1JlZ2lvbk1hcCIsImNvbnRhYm9PU01hcCIsImRpc3BsYXlOYW1lIiwiRGF0ZSIsIm5vdyIsImNhcnREYXRhIiwicGVyaW9kIiwiY3VzdG9tQ29uZmlndXJhdGlvbiIsInZwc0NvbmZpZyIsInByb3ZpZGVyUHJvZHVjdElkIiwib3BlcmF0aW5nU3lzdGVtIiwicm9vdFBhc3N3b3JkIiwic3NoS2V5cyIsInVzZXJEYXRhIiwiYWRkb25zIiwicHJpdmF0ZW5ldHdvcmtpbmciLCJhdXRvYmFja3VwIiwiY3B1IiwiYmFuZHdpZHRoIiwiZnJvbnRlbmRDb25maWciLCJsb2NhdGlvbiIsImJhY2t1cCIsInBsYW5OYW1lIiwiYWRkSXRlbVRvQ2FydCIsImNhcnQiLCJjYXJ0Q291bnQiLCJzdWNjZXNzIiwicHVzaCIsInN0YXR1cyIsIm9zIiwiaWNvbiIsImdldE9TSWNvbiIsImljb25NYXAiLCJsb3dlclR5cGUiLCJmbGFnIiwiZ2V0UmVnaW9uRmxhZyIsInBpbmciLCJnZXRSZWdpb25QaW5nIiwiZmxhZ01hcCIsInJlZ2lvbklkIiwicGluZ01hcCIsImNhbGN1bGF0ZVRvdGFsIiwidG90YWwiLCJvYmplY3RTdG9yYWdlQ29zdHMiLCJzc2xDb3N0cyIsIm11bHRpcGxpZXJzIiwibW9udGhseSIsImFubnVhbGx5IiwidmFyaWFudCIsIm9uQ2xpY2siLCJ3aW5kb3ciLCJyZWxvYWQiLCJjb2xvciIsInNpemUiLCJoaXN0b3J5IiwiYmFjayIsInNwYW4iLCJsYWJlbCIsImRpc2NvdW50Iiwicm91bmQiLCJidXR0b24iLCJmaWxsUnVsZSIsImNsaXBSdWxlIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJvcHRpb24iLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwibWF4IiwiZGlzYWJsZWQiLCJtaW4iLCJociIsInN0cm9uZyIsImxvYyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx\n"));

/***/ })

});