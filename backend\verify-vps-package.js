/**
 * Verify VPS Package Script
 * This script uses the backend's existing database connection to check for VPS packages
 */

const Package = require('./models/Package');
const Category = require('./models/Category');
const Brand = require('./models/Brand');

async function verifyVPSPackage() {
  try {
    console.log('🔍 Verifying VPS packages using backend models...\n');

    // Check total packages
    const totalPackages = await Package.countDocuments();
    console.log(`📦 Total packages in database: ${totalPackages}`);

    if (totalPackages === 0) {
      console.log('⚠️  No packages found in database');
      return;
    }

    // Get all packages
    const allPackages = await Package.find({}).populate('category brand');
    console.log('\n📋 All packages:');
    
    allPackages.forEach((pkg, index) => {
      console.log(`\n   ${index + 1}. ${pkg.name}`);
      console.log(`      ID: ${pkg._id}`);
      console.log(`      Price: ${pkg.price} MAD`);
      console.log(`      Category: ${pkg.category?.name || 'Not set'}`);
      console.log(`      Brand: ${pkg.brand?.name || 'Not set'}`);
      console.log(`      Active: ${pkg.isActive}`);
      console.log(`      VPS Config: ${JSON.stringify(pkg.vpsConfig || 'Not set')}`);
    });

    // Check specifically for VPS packages
    const vpsPackages = await Package.find({ 
      'vpsConfig.provider': { $exists: true } 
    }).populate('category brand');
    
    console.log(`\n🎯 VPS packages found: ${vpsPackages.length}`);

    if (vpsPackages.length > 0) {
      console.log('\n✅ VPS Packages details:');
      vpsPackages.forEach((pkg, index) => {
        console.log(`\n   ${index + 1}. ${pkg.name}`);
        console.log(`      ID: ${pkg._id}`);
        console.log(`      Price: ${pkg.price} MAD`);
        console.log(`      Provider: ${pkg.vpsConfig.provider}`);
        console.log(`      Product ID: ${pkg.vpsConfig.providerProductId}`);
        console.log(`      Category: ${pkg.category?.name || 'Not set'}`);
        console.log(`      Brand: ${pkg.brand?.name || 'Not set'}`);
        
        if (pkg.vpsConfig.providerProductId === 'V91') {
          console.log('      🎯 THIS IS YOUR V91 PACKAGE!');
        }
      });

      // Test the API endpoint with the first VPS package
      console.log('\n🧪 Testing API endpoint with VPS package...');
      await testAPIEndpoint(vpsPackages[0]);

    } else {
      console.log('\n❌ No VPS packages found with vpsConfig.provider');
      
      // Check for packages that might have V91 but missing vpsConfig structure
      const v91Packages = await Package.find({
        $or: [
          { name: /V91/i },
          { description: /V91/i },
          { 'vpsConfig.providerProductId': 'V91' }
        ]
      });
      
      if (v91Packages.length > 0) {
        console.log('\n📋 Found packages mentioning V91:');
        v91Packages.forEach((pkg, index) => {
          console.log(`   ${index + 1}. ${pkg.name}`);
          console.log(`      VPS Config: ${JSON.stringify(pkg.vpsConfig || 'MISSING')}`);
        });
      }
    }

    // Check categories and brands
    const vpsCategory = await Category.findOne({ name: 'VPS' });
    const contaboBrand = await Brand.findOne({ name: 'Contabo' });
    
    console.log(`\n📊 Supporting data:`);
    console.log(`   VPS Category: ${vpsCategory ? '✅ Exists' : '❌ Missing'}`);
    console.log(`   Contabo Brand: ${contaboBrand ? '✅ Exists' : '❌ Missing'}`);

  } catch (error) {
    console.error('\n❌ Error verifying packages:', error.message);
  }
}

async function testAPIEndpoint(vpsPackage) {
  const axios = require('axios');
  
  try {
    // Test the package endpoint
    const response = await axios.get('http://localhost:5002/package/get-packages');
    const apiPackages = response.data.data || [];
    
    console.log(`   API returned ${apiPackages.length} packages`);
    
    const apiVpsPackages = apiPackages.filter(pkg => 
      pkg.vpsConfig && pkg.vpsConfig.provider
    );
    
    console.log(`   API VPS packages: ${apiVpsPackages.length}`);
    
    if (apiVpsPackages.length > 0) {
      console.log('   ✅ VPS package is accessible via API');
      
      // Try to create a VPS order
      console.log('\n🚀 Testing VPS order creation...');
      await testVPSOrder(apiVpsPackages[0]);
    } else {
      console.log('   ❌ VPS package not accessible via API');
    }
    
  } catch (error) {
    console.log(`   ❌ API test failed: ${error.message}`);
  }
}

async function testVPSOrder(vpsPackage) {
  const axios = require('axios');
  
  const orderData = {
    packageId: vpsPackage._id,
    region: "EU",
    operatingSystem: "ubuntu-22.04",
    displayName: "test-vps-server",
    billingCycle: "monthly",
    billingInfo: {
      name: "Test User",
      email: "<EMAIL>",
      phone: "+**********",
      address: "123 Test Street",
      country: "US",
      isCompany: false,
    },
    sshKeys: [],
  };

  try {
    const orderResponse = await axios.post(
      "http://localhost:5002/vps/order",
      orderData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MjIwMmYyOTkyZDE2Y2ZiYzQ2NDAwOCIsIm5hbWUiOiJSw6hkYSIsImVtYWlsIjoidHJvdWtpMjEzQGdtYWlsLmNvbSIsInBob3RvIjoiaHR0cHM6Ly9saDMuZ29vZ2xldXNlcmNvbnRlbnQuY29tL2EvQUNnOG9jSjdTOFBvZFppdllITEt4Vng0THZsY1dJazNJcktLX2xkbDFrdkJMNkM3X2xXZFVETmI9czk2LWMiLCJyb2xlIjoiQURNSU4iLCJpYXQiOjE3NTMwOTYzMTYsImV4cCI6MTc1MzE4MjcxNn0.X6AJd51469PrGehvKXLgQgIqxH-wgwOmv1yfcry02yQ'
        }
      }
    );
    
    console.log('   ✅ VPS order created successfully!');
    console.log(`   Order ID: ${orderResponse.data.data?.orderId}`);
    console.log(`   Status: ${orderResponse.data.data?.status}`);
    
  } catch (error) {
    console.log('   ❌ VPS order creation failed');
    console.log(`   Status: ${error.response?.status}`);
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
    
    if (error.response?.data?.errors) {
      console.log('   Validation Errors:');
      error.response.data.errors.forEach((err, index) => {
        console.log(`     ${index + 1}. ${err.key}: ${err.msg}`);
      });
    }
  }
}

// Run the verification
verifyVPSPackage()
  .then(() => {
    console.log('\n🏁 Verification complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Verification failed:', error.message);
    process.exit(1);
  });
