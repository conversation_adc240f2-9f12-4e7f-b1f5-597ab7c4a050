/**
 * Mark Order as <PERSON><PERSON>
 * 
 * This script marks an order as paid to trigger VPS provisioning.
 * 
 * Usage: 
 *   node mark-order-paid.js ORDER_ID
 *   
 * Example:
 *   node mark-order-paid.js ABC123DEF
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5002';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MjIwMmYyOTkyZDE2Y2ZiYzQ2NDAwOCIsIm5hbWUiOiJSw6hkYSIsImVtYWlsIjoidHJvdWtpMjEzQGdtYWlsLmNvbSIsInBob3RvIjoiaHR0cHM6Ly9saDMuZ29vZ2xldXNlcmNvbnRlbnQuY29tL2EvQUNnOG9jSjdTOFBvZFppdllITEt4Vng0THZsY1dJazNJcktLX2xkbDFrdkJMNkM3X2xXZFVETmI9czk2LWMiLCJyb2xlIjoiQURNSU4iLCJpYXQiOjE3NTMwOTYzMTYsImV4cCI6MTc1MzE4MjcxNn0.X6AJd51469PrGehvKXLgQgIqxH-wgwOmv1yfcry02yQ';

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `token=${ADMIN_TOKEN}`
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ Request failed: ${method} ${endpoint}`);
    console.error('   Status:', error.response?.status);
    console.error('   Error:', error.response?.data?.message || error.message);
    throw error;
  }
};

async function markOrderAsPaid(orderId) {
  try {
    console.log('💳 Marking order as paid to trigger VPS provisioning...\n');
    console.log(`📋 Order ID: ${orderId}`);

    // Step 1: Get order details first
    console.log('\n1. 🔍 Fetching order details...');
    try {
      const orderDetails = await makeRequest('GET', `/admin/orders/${orderId}`);
      
      if (orderDetails.success && orderDetails.data) {
        const order = orderDetails.data;
        console.log('✅ Order found:');
        console.log(`   Order ID: ${order.identifiant}`);
        console.log(`   Status: ${order.status}`);
        console.log(`   Total Price: ${order.totalPrice} MAD`);
        console.log(`   User: ${order.user?.name || 'Unknown'}`);
        console.log(`   Sub Orders: ${order.subOrders?.length || 0}`);
        
        // Check for VPS sub-orders
        const vpsSubOrders = order.subOrders?.filter(sub => 
          sub.vps || (sub.package && sub.package.vpsConfig)
        ) || [];
        
        console.log(`   VPS Sub Orders: ${vpsSubOrders.length}`);
        
        if (vpsSubOrders.length > 0) {
          console.log('   📦 VPS Packages in order:');
          vpsSubOrders.forEach((sub, index) => {
            console.log(`     ${index + 1}. ${sub.package?.name || 'Unknown Package'}`);
            console.log(`        Provider: ${sub.vps?.provider || sub.package?.vpsConfig?.provider || 'Unknown'}`);
            console.log(`        Plan ID: ${sub.vps?.planId || sub.package?.vpsConfig?.providerProductId || 'Unknown'}`);
            console.log(`        Status: ${sub.vps?.status || sub.status || 'Unknown'}`);
          });
        }
        
        if (order.isPaid) {
          console.log('⚠️  Order is already marked as paid');
          return;
        }
        
      } else {
        console.log('❌ Order not found or invalid response');
        return;
      }
    } catch (error) {
      console.log('❌ Failed to fetch order details');
      console.log('   This might be normal if the order endpoint has different structure');
    }

    // Step 2: Mark order as paid using the order service
    console.log('\n2. 💳 Marking order as paid...');
    try {
      const paidResult = await makeRequest('POST', `/order/mark-paid/${orderId}`, {
        paymentMethod: 'SIMULATION',
        transactionId: `SIM_${Date.now()}`,
        paidAmount: null, // Will use order total
        notes: 'Marked as paid via script for VPS provisioning test'
      });
      
      if (paidResult.success) {
        console.log('✅ Order marked as paid successfully!');
        console.log(`   Transaction ID: ${paidResult.data?.transactionId || 'Generated'}`);
        console.log(`   Payment Method: ${paidResult.data?.paymentMethod || 'SIMULATION'}`);
      }
      
    } catch (error) {
      // Try alternative endpoint
      console.log('   Trying alternative payment confirmation...');
      
      try {
        const altResult = await makeRequest('POST', `/vps/confirm-payment`, {
          orderId: orderId,
          paymentStatus: 'completed',
          transactionId: `SIM_${Date.now()}`,
          paymentMethod: 'simulation'
        });
        
        if (altResult.success) {
          console.log('✅ Payment confirmed via VPS endpoint!');
        }
        
      } catch (altError) {
        console.log('❌ Both payment methods failed');
        console.log('   You may need to mark the order as paid manually in the admin panel');
      }
    }

    // Step 3: Check if VPS provisioning started
    console.log('\n3. 🚀 Checking VPS provisioning status...');
    
    // Wait a moment for processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    try {
      // Try to get updated order status
      const updatedOrder = await makeRequest('GET', `/admin/orders/${orderId}`);
      
      if (updatedOrder.success && updatedOrder.data) {
        const order = updatedOrder.data;
        console.log('📊 Updated order status:');
        console.log(`   Order Status: ${order.status}`);
        console.log(`   Is Paid: ${order.isPaid ? '✅ Yes' : '❌ No'}`);
        
        const vpsSubOrders = order.subOrders?.filter(sub => 
          sub.vps || (sub.package && sub.package.vpsConfig)
        ) || [];
        
        if (vpsSubOrders.length > 0) {
          console.log('   VPS Provisioning Status:');
          vpsSubOrders.forEach((sub, index) => {
            console.log(`     ${index + 1}. ${sub.package?.name || 'VPS Package'}`);
            console.log(`        Sub Order Status: ${sub.status}`);
            console.log(`        VPS Status: ${sub.vps?.status || 'Not set'}`);
            
            if (sub.vps?.providerInstanceId) {
              console.log(`        Provider Instance ID: ${sub.vps.providerInstanceId}`);
            }
            
            if (sub.vps?.ipAddress) {
              console.log(`        IP Address: ${sub.vps.ipAddress}`);
            }
          });
        }
      }
      
    } catch (error) {
      console.log('⚠️  Could not fetch updated order status');
    }

    // Step 4: Check VPS instances
    console.log('\n4. 🖥️  Checking VPS instances...');
    try {
      const vpsInstances = await makeRequest('GET', '/vps/instances');
      
      if (vpsInstances.success && vpsInstances.data) {
        const instances = vpsInstances.data;
        console.log(`   Total VPS instances: ${instances.length}`);
        
        // Look for recent instances
        const recentInstances = instances.filter(instance => {
          const createdAt = new Date(instance.createdAt);
          const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
          return createdAt > fiveMinutesAgo;
        });
        
        if (recentInstances.length > 0) {
          console.log('   🎉 Recent VPS instances found:');
          recentInstances.forEach((instance, index) => {
            console.log(`     ${index + 1}. ${instance.config?.name || 'Unnamed VPS'}`);
            console.log(`        Status: ${instance.status}`);
            console.log(`        Provider: ${instance.config?.provider}`);
            console.log(`        IP: ${instance.network?.ipv4 || 'Not assigned'}`);
            console.log(`        Created: ${new Date(instance.createdAt).toLocaleString()}`);
          });
        } else {
          console.log('   ⏳ No recent VPS instances found yet');
          console.log('   💡 VPS provisioning may take a few minutes to complete');
        }
      }
      
    } catch (error) {
      console.log('⚠️  Could not fetch VPS instances (may require user authentication)');
    }

    console.log('\n🎉 PROCESS COMPLETE!');
    console.log('\n📋 SUMMARY:');
    console.log('✅ Order payment simulation completed');
    console.log('🚀 VPS provisioning should have started');
    console.log('⏳ VPS creation may take 2-5 minutes to complete');
    console.log('📧 User will receive VPS details via email when ready');
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. Check the admin panel for order status updates');
    console.log('2. Monitor VPS instances in the admin dashboard');
    console.log('3. Check server logs for provisioning progress');
    console.log('4. Verify email notifications are sent to the user');

  } catch (error) {
    console.error('\n❌ Failed to mark order as paid:', error.message);
    
    console.log('\n💡 TROUBLESHOOTING:');
    console.log('1. Make sure the order ID is correct');
    console.log('2. Verify the backend server is running');
    console.log('3. Check if the admin token is valid');
    console.log('4. Try marking the order as paid manually in the admin panel');
  }
}

// Get order ID from command line arguments
const orderId = process.argv[2];

if (!orderId) {
  console.log('❌ Please provide an order ID');
  console.log('\nUsage: node mark-order-paid.js ORDER_ID');
  console.log('Example: node mark-order-paid.js ABC123DEF');
  process.exit(1);
}

// Run the script
markOrderAsPaid(orderId);
