const mongoose = require('mongoose');
const Package = require('./models/Package');
const Category = require('./models/Category');
const Brand = require('./models/Brand');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://jakiezian:<EMAIL>/ztech?retryWrites=true&w=majority&appName=ztech-dev');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    process.exit(1);
  }
};

// Create or get VPS category
const createVPSCategory = async () => {
  try {
    let vpsCategory = await Category.findOne({ name: 'VPS' });
    
    if (!vpsCategory) {
      vpsCategory = new Category({
        name: 'VPS',
        description: 'Virtual Private Server hosting solutions',
        isActive: true
      });
      await vpsCategory.save();
      console.log('✅ Created VPS category');
    } else {
      console.log('✅ VPS category already exists');
    }
    
    return vpsCategory;
  } catch (error) {
    console.error('❌ Error creating VPS category:', error.message);
    throw error;
  }
};

// Create or get Contabo brand
const createContaboBrand = async () => {
  try {
    let contaboBrand = await Brand.findOne({ name: 'Contabo' });
    
    if (!contaboBrand) {
      contaboBrand = new Brand({
        name: 'Contabo',
        description: 'High-performance VPS hosting provider',
        isActive: true
      });
      await contaboBrand.save();
      console.log('✅ Created Contabo brand');
    } else {
      console.log('✅ Contabo brand already exists');
    }
    
    return contaboBrand;
  } catch (error) {
    console.error('❌ Error creating Contabo brand:', error.message);
    throw error;
  }
};

// VPS packages data based on Contabo plans
const vpsPackagesData = [
  {
    name: 'VPS 10 NVMe',
    description: 'Entry-level VPS with NVMe storage - Perfect for small websites and development',
    price: 150, // 4.99 EUR ≈ 150 MAD (approximate conversion)
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V91'
    },
    specifications: {
      cpu: '1 vCPU',
      ram: '4 GB RAM',
      storage: '75 GB NVMe SSD',
      bandwidth: '32 TB Traffic'
    }
  },
  {
    name: 'VPS 10 SSD',
    description: 'Entry-level VPS with SSD storage - Reliable performance for small projects',
    price: 150, // 4.99 EUR ≈ 150 MAD
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V92'
    },
    specifications: {
      cpu: '1 vCPU',
      ram: '4 GB RAM',
      storage: '100 GB SSD',
      bandwidth: '32 TB Traffic'
    }
  },
  {
    name: 'VPS 20 NVMe',
    description: 'Mid-range VPS with NVMe storage - Great for growing applications',
    price: 270, // 8.99 EUR ≈ 270 MAD
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V94'
    },
    specifications: {
      cpu: '2 vCPU',
      ram: '8 GB RAM',
      storage: '150 GB NVMe SSD',
      bandwidth: '32 TB Traffic'
    }
  },
  {
    name: 'VPS 20 SSD',
    description: 'Mid-range VPS with SSD storage - Balanced performance and storage',
    price: 270, // 8.99 EUR ≈ 270 MAD
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V95'
    },
    specifications: {
      cpu: '2 vCPU',
      ram: '8 GB RAM',
      storage: '200 GB SSD',
      bandwidth: '32 TB Traffic'
    }
  },
  {
    name: 'VPS 30 NVMe',
    description: 'High-performance VPS with NVMe storage - For demanding applications',
    price: 450, // 14.99 EUR ≈ 450 MAD
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V97'
    },
    specifications: {
      cpu: '4 vCPU',
      ram: '16 GB RAM',
      storage: '300 GB NVMe SSD',
      bandwidth: '32 TB Traffic'
    }
  },
  {
    name: 'VPS 30 SSD',
    description: 'High-performance VPS with SSD storage - Enterprise-grade reliability',
    price: 450, // 14.99 EUR ≈ 450 MAD
    vpsConfig: {
      provider: 'contabo',
      providerProductId: 'V98'
    },
    specifications: {
      cpu: '4 vCPU',
      ram: '16 GB RAM',
      storage: '400 GB SSD',
      bandwidth: '32 TB Traffic'
    }
  }
];

// Create VPS packages
const createVPSPackages = async (category, brand) => {
  try {
    console.log('🚀 Creating VPS packages...');
    
    for (const packageData of vpsPackagesData) {
      // Check if package already exists
      const existingPackage = await Package.findOne({ 
        name: packageData.name,
        'vpsConfig.provider': packageData.vpsConfig.provider,
        'vpsConfig.providerProductId': packageData.vpsConfig.providerProductId
      });
      
      if (existingPackage) {
        console.log(`⚠️  Package "${packageData.name}" already exists, skipping...`);
        continue;
      }
      
      const vpsPackage = new Package({
        name: packageData.name,
        description: packageData.description,
        price: packageData.price,
        category: category._id,
        brand: brand._id,
        isActive: true,
        vpsConfig: packageData.vpsConfig,
        specifications: [
          { name: 'CPU', value: packageData.specifications.cpu },
          { name: 'RAM', value: packageData.specifications.ram },
          { name: 'Storage', value: packageData.specifications.storage },
          { name: 'Bandwidth', value: packageData.specifications.bandwidth }
        ]
      });
      
      await vpsPackage.save();
      console.log(`✅ Created VPS package: ${packageData.name} (${packageData.vpsConfig.providerProductId})`);
    }
    
    console.log('🎉 All VPS packages created successfully!');
  } catch (error) {
    console.error('❌ Error creating VPS packages:', error.message);
    throw error;
  }
};

// Main setup function
const setupVPSPackages = async () => {
  try {
    console.log('🚀 Starting VPS packages setup...\n');
    
    // Connect to database
    await connectDB();
    
    // Create category and brand
    const vpsCategory = await createVPSCategory();
    const contaboBrand = await createContaboBrand();
    
    // Create VPS packages
    await createVPSPackages(vpsCategory, contaboBrand);
    
    // Verify packages were created
    const vpsPackages = await Package.find({ 
      'vpsConfig.provider': { $exists: true } 
    }).populate('category brand');
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total VPS packages in database: ${vpsPackages.length}`);
    console.log(`   Packages created:`);
    
    vpsPackages.forEach((pkg, index) => {
      console.log(`   ${index + 1}. ${pkg.name} - ${pkg.price} MAD (${pkg.vpsConfig.providerProductId})`);
    });
    
    console.log('\n✅ VPS packages setup completed successfully!');
    console.log('🎯 You can now test VPS creation through the frontend or API.');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed.');
    process.exit(0);
  }
};

// Run the setup
if (require.main === module) {
  setupVPSPackages();
}

module.exports = { setupVPSPackages };
