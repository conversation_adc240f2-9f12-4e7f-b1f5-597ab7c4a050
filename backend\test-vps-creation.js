const axios = require("axios");

async function testVPSCreation() {
  try {
    console.log("🧪 Testing VPS Creation Process...\n");

    // Test 1: Check if backend is running
    console.log("1. Testing backend connectivity...");
    try {
      const healthCheck = await axios.get("http://localhost:5002/");
      console.log("✅ Backend is running");
    } catch (error) {
      console.log("❌ Backend is not running or not accessible");
      console.log("   Error:", error.message);
      return;
    }

    // Test 2: Check VPS images endpoint
    console.log("\n2. Testing VPS images endpoint...");
    try {
      const imagesResponse = await axios.get(
        "http://localhost:5002/vps/images?provider=contabo"
      );
      console.log("✅ VPS images endpoint working");
      console.log(`   Found ${imagesResponse.data.data?.length || 0} images`);
    } catch (error) {
      console.log("❌ VPS images endpoint failed");
      console.log("   Error:", error.response?.data?.message || error.message);
    }

    // Test 3: Check VPS regions endpoint
    console.log("\n3. Testing VPS regions endpoint...");
    try {
      const regionsResponse = await axios.get(
        "http://localhost:5002/vps/regions?provider=contabo"
      );
      console.log("✅ VPS regions endpoint working");
      console.log(`   Found ${regionsResponse.data.data?.length || 0} regions`);
    } catch (error) {
      console.log("❌ VPS regions endpoint failed");
      console.log("   Error:", error.response?.data?.message || error.message);
    }

    // Test 4: Test Contabo API authentication
    console.log("\n4. Testing Contabo API authentication...");
    try {
      const ContaboProvider = require("./services/providers/ContaboProvider");
      const provider = new ContaboProvider();
      const isValid = await provider.validateConfig();
      if (isValid) {
        console.log("✅ Contabo API authentication successful");
      } else {
        console.log("❌ Contabo API authentication failed");
      }
    } catch (error) {
      console.log("❌ Contabo API authentication error");
      console.log("   Error:", error.message);
    }

    // Test 5: Test VPS package retrieval
    console.log("\n5. Testing VPS package retrieval...");
    try {
      const packagesResponse = await axios.get(
        "http://localhost:5002/package/get-packages"
      );
      const vpsPackages =
        packagesResponse.data.data?.filter(
          (pkg) => pkg.vpsConfig && pkg.vpsConfig.provider
        ) || [];
      console.log(`✅ Found ${vpsPackages.length} VPS packages`);

      if (vpsPackages.length > 0) {
        console.log("   VPS Packages:");
        vpsPackages.forEach((pkg, index) => {
          console.log(
            `   ${index + 1}. ${pkg.name} (${pkg.vpsConfig.provider}:${
              pkg.vpsConfig.providerProductId
            }) - ${pkg.price} MAD`
          );
        });
      }
    } catch (error) {
      console.log("❌ VPS package retrieval failed");
      console.log("   Error:", error.response?.data?.message || error.message);
    }

    console.log("\n🏁 VPS Creation Test Complete");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the test
testVPSCreation();
