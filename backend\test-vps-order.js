const axios = require("axios");

// Test VPS order creation with a mock package
async function testVPSOrder() {
  try {
    console.log("🧪 Testing VPS Order Creation...\n");

    // First, let's try to create a VPS order directly
    console.log("1. Testing VPS order creation...");

    const orderData = {
      packageId: "test-vps-package-id", // Required field - this needs to be a real VPS package ID
      region: "EU",
      operatingSystem: "ubuntu-22.04",
      displayName: "test-vps-server",
      billingCycle: "monthly",
      billingInfo: {
        name: "Test User",
        email: "<EMAIL>",
        phone: "+1234567890",
        address: "123 Test Street",
        country: "US",
        isCompany: false,
      },
      sshKeys: [],
    };

    try {
      const orderResponse = await axios.post(
        "http://localhost:5002/vps/order",
        orderData,
        {
          headers: {
            "Content-Type": "application/json",
            Cookie:
              "token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MjIwMmYyOTkyZDE2Y2ZiYzQ2NDAwOCIsIm5hbWUiOiJSw6hkYSIsImVtYWlsIjoidHJvdWtpMjEzQGdtYWlsLmNvbSIsInBob3RvIjoiaHR0cHM6Ly9saDMuZ29vZ2xldXNlcmNvbnRlbnQuY29tL2EvQUNnOG9jSjdTOFBvZFppdllITEt4Vng0THZsY1dJazNJcktLX2xkbDFrdkJMNkM3X2xXZFVETmI9czk2LWMiLCJyb2xlIjoiQURNSU4iLCJpYXQiOjE3NTMwOTYzMTYsImV4cCI6MTc1MzE4MjcxNn0.X6AJd51469PrGehvKXLgQgIqxH-wgwOmv1yfcry02yQ",
          },
        }
      );

      console.log("✅ VPS order created successfully");
      console.log("   Order ID:", orderResponse.data.data?.orderId);
      console.log("   Status:", orderResponse.data.data?.status);
    } catch (error) {
      console.log("❌ VPS order creation failed");
      console.log("   Status:", error.response?.status);
      console.log("   Error:", error.response?.data?.message || error.message);

      if (error.response?.data?.errors) {
        console.log("   Validation Errors:");
        error.response.data.errors.forEach((err, index) => {
          console.log(`     ${index + 1}. ${err.key}: ${err.msg}`);
        });
      }

      if (error.response?.data?.details) {
        console.log("   Details:", error.response.data.details);
      }
    }

    // Test 2: Check VPS plans endpoint
    console.log("\n2. Testing VPS plans endpoint...");
    try {
      const plansResponse = await axios.get(
        "http://localhost:5002/vps/plans?provider=contabo"
      );
      console.log("✅ VPS plans endpoint working");
      console.log(`   Found ${plansResponse.data.data?.length || 0} plans`);

      if (plansResponse.data.data?.length > 0) {
        console.log("   Available plans:");
        plansResponse.data.data.slice(0, 3).forEach((plan, index) => {
          console.log(
            `   ${index + 1}. ${plan.name} (${plan.id}) - ${
              plan.price?.monthly || "N/A"
            } EUR/month`
          );
        });
      }
    } catch (error) {
      console.log("❌ VPS plans endpoint failed");
      console.log("   Error:", error.response?.data?.message || error.message);
    }

    // Test 3: Test cart functionality with VPS
    console.log("\n3. Testing cart with VPS configuration...");
    try {
      const cartData = {
        packageId: "mock-vps-package", // This will likely fail, but let's see the error
        quantity: 1,
        period: 1,
        customConfiguration: orderData,
      };

      const cartResponse = await axios.post(
        "http://localhost:5002/cart/add-item",
        cartData,
        {
          headers: {
            "Content-Type": "application/json",
            Cookie:
              "token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MjIwMmYyOTkyZDE2Y2ZiYzQ2NDAwOCIsIm5hbWUiOiJSw6hkYSIsImVtYWlsIjoidHJvdWtpMjEzQGdtYWlsLmNvbSIsInBob3RvIjoiaHR0cHM6Ly9saDMuZ29vZ2xldXNlcmNvbnRlbnQuY29tL2EvQUNnOG9jSjdTOFBvZFppdllITEt4Vng0THZsY1dJazNJcktLX2xkbDFrdkJMNkM3X2xXZFVETmI9czk2LWMiLCJyb2xlIjoiQURNSU4iLCJpYXQiOjE3NTMwOTYzMTYsImV4cCI6MTc1MzE4MjcxNn0.X6AJd51469PrGehvKXLgQgIqxH-wgwOmv1yfcry02yQ",
          },
        }
      );

      console.log("✅ VPS added to cart successfully");
      console.log("   Cart item ID:", cartResponse.data.data?.itemId);
    } catch (error) {
      console.log("❌ Adding VPS to cart failed");
      console.log("   Status:", error.response?.status);
      console.log("   Error:", error.response?.data?.message || error.message);
    }

    console.log("\n🏁 VPS Order Test Complete");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the test
testVPSOrder();
